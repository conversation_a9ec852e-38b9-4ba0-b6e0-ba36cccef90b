import axios from 'axios';
import { getCookie } from '@/utils/cookies';
import { API_URL } from '@/configs/global.config';

interface AxiosProps {
	customUrl?: string;
	auth?: boolean;
	contentType?: string;
	apiKey?: boolean;
}

export const CallAPI = ({
	contentType = 'application/json',
	auth = true,
	customUrl = '',
	apiKey = false,
}: AxiosProps = {}) => {
	const axiosInstance = axios.create({
		baseURL: customUrl || API_URL || process.env.NEXT_PUBLIC_API_URL,
	});

	axiosInstance.interceptors.request.use(
		async (config) => {
			config.headers['Content-Type'] = contentType;
			config.headers['Accept'] = '*/*';

			if (auth) {
				const token = getCookie('access_token');
				if (token) {
					config.headers['Authorization'] = `Bearer ${token}`;
				}
			}
			if (apiKey) {
				console.log('abc');
				config.headers['x-access-token'] = process.env.ACCESS_TOKEN;
				config.headers['x-profile-id'] = process.env.PROFILE_ID;
			}
			return config;
		},
		(error) => {
			return Promise.reject(error);
		}
	);

	// Thêm interceptor response để xử lý lỗi CORS nếu có
	axiosInstance.interceptors.response.use(
		(response) => {
			return response;
		},
		(error) => {
			return Promise.reject(error);
		}
	);

	return axiosInstance;
};
