import localFont from 'next/font/local';

export const interTight = localFont({
	src: [
		// Regular (400)
		{
			path: './resource/InterTight-Regular.ttf',
			weight: '400',
			style: 'normal',
		},
		{
			path: './resource/InterTight-Italic.ttf',
			weight: '400',
			style: 'italic',
		},
		// Thin (100)
		// {
		// 	path: './resource/InterTight-Thin.ttf',
		// 	weight: '100',
		// 	style: 'normal',
		// },
		// {
		// 	path: './resource/InterTight-ThinItalic.ttf',
		// 	weight: '100',
		// 	style: 'italic',
		// },
		// ExtraLight (200)
		// {
		// 	path: './resource/InterTight-ExtraLight.ttf',
		// 	weight: '200',
		// 	style: 'normal',
		// },
		// {
		// 	path: './resource/InterTight-ExtraLightItalic.ttf',
		// 	weight: '200',
		// 	style: 'italic',
		// },
		// Light (300)
		// {
		// 	path: './resource/InterTight-Light.ttf',
		// 	weight: '300',
		// 	style: 'normal',
		// },
		// {
		// 	path: './resource/InterTight-LightItalic.ttf',
		// 	weight: '300',
		// 	style: 'italic',
		// },
		// Medium (500)
		{
			path: './resource/InterTight-Medium.ttf',
			weight: '500',
			style: 'normal',
		},
		{
			path: './resource/InterTight-MediumItalic.ttf',
			weight: '500',
			style: 'italic',
		},
		// SemiBold (600)
		{
			path: './resource/InterTight-SemiBold.ttf',
			weight: '600',
			style: 'normal',
		},
		{
			path: './resource/InterTight-SemiBoldItalic.ttf',
			weight: '600',
			style: 'italic',
		},
		// Bold (700)
		{
			path: './resource/InterTight-Bold.ttf',
			weight: '700',
			style: 'normal',
		},
		{
			path: './resource/InterTight-BoldItalic.ttf',
			weight: '700',
			style: 'italic',
		},
		// ExtraBold (800)
		{
			path: './resource/InterTight-ExtraBold.ttf',
			weight: '800',
			style: 'normal',
		},
		{
			path: './resource/InterTight-ExtraBoldItalic.ttf',
			weight: '800',
			style: 'italic',
		},
		// Black (900)
		// {
		// 	path: './resource/InterTight-Black.ttf',
		// 	weight: '900',
		// 	style: 'normal',
		// },
		// {
		// 	path: './resource/InterTight-BlackItalic.ttf',
		// 	weight: '900',
		// 	style: 'italic',
		// },
	],
	display: 'swap', // Ensures font loads smoothly during fallback
	variable: '--font-inter-tight', // Optional: CSS variable for global use
});
