/**
 * Tạo query string từ object, loại bỏ các giá trị rỗng
 * @param params Object chứa các tham số cần chuyển thành query string
 * @returns Chuỗi query string đã được xử lý
 */
export function createQueryString<T extends Record<string, any>>(params: T): string {
	// Lọc bỏ các giá trị undefined, null, và chuỗi rỗng
	const filteredParams = Object.entries(params).filter(
		([_, value]) =>
			value !== undefined &&
			value !== null &&
			value !== ''
	);

	// Tạo URLSearchParams từ các tham số đã lọc
	const searchParams = new URLSearchParams();

	// Thêm các tham số vào URLSearchParams
	filteredParams.forEach(([key, value]) => {
		// Xử lý mảng nếu cần
		if (Array.isArray(value)) {
			if (value.length > 0) {
				searchParams.append(key, value.join(','));
			}
		}
		// Chuyển đổi các đối tượng nếu cần
		else if (typeof value === 'object' && value !== null) {
			searchParams.append(key, JSON.stringify(value));
		}
		// Xử lý các giá trị cơ bản
		else {
			searchParams.append(key, String(value));
		}
	});

	return searchParams.toString();
}

/**
 * Tạo URL đầy đủ từ baseUrl và đối tượng params
 * @param baseUrl URL cơ sở
 * @param params Object chứa các tham số
 * @returns URL đầy đủ với query string
 */
export function createUrl(baseUrl: string, params: Record<string, any>): string {
	const queryString = createQueryString(params);

	// Nếu không có query string, trả về baseUrl
	if (!queryString) return baseUrl;

	// Thêm dấu ? nếu baseUrl không có sẵn
	const separator = baseUrl.includes('?') ? '&' : '?';

	return `${baseUrl}${separator}${queryString}`;
}

/**
 * Phân tích query string thành object
 * @param queryString Chuỗi query cần phân tích
 * @returns Object chứa các tham số từ query string
 */
export function parseQueryString(queryString: string): Record<string, string> {
	if (!queryString || queryString.trim() === '') return {};

	// Loại bỏ dấu ? ở đầu nếu có
	const sanitizedQuery = queryString.startsWith('?')
		? queryString.substring(1)
		: queryString;

	const params = new URLSearchParams(sanitizedQuery);
	const result: Record<string, string> = {};

	params.forEach((value, key) => {
		result[key] = value;
	});

	return result;
}

/**
 * Gộp nhiều params object thành một
 * @param objects Danh sách các object params cần gộp
 * @returns Object params đã được gộp
 */
export function mergeQueryParams(...objects: Record<string, any>[]): Record<string, any> {
	return objects.reduce((acc, obj) => {
		return { ...acc, ...obj };
	}, {});
}
