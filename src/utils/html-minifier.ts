/**
 * Minify HTML content by removing unnecessary whitespace, line breaks, and formatting
 * @param html - The HTML string to minify
 * @returns Minified HTML string
 */
export const minifyHtml = (html: string): string => {
	return html
		// Remove HTML comments
		.replace(/<!--[\s\S]*?-->/g, '')
		// Remove multiple whitespace between tags
		.replace(/>\s+</g, '><')
		// Remove leading and trailing whitespace
		.trim()
		// Remove line breaks and tabs
		.replace(/[\r\n\t]/g, '')
		// Remove multiple spaces
		.replace(/\s{2,}/g, ' ')
		// Remove whitespace around = in attributes
		.replace(/\s*=\s*/g, '=')
		// Remove whitespace before closing tags
		.replace(/\s+>/g, '>')
		// Remove whitespace after opening tags
		.replace(/>\s+/g, '>')
		// Remove whitespace before opening tags
		.replace(/\s+</g, '<');
};

/**
 * Calculate the compression ratio
 * @param original - Original HTML string
 * @param minified - Minified HTML string
 * @returns Compression percentage
 */
export const getCompressionRatio = (original: string, minified: string): number => {
	const originalSize = original.length;
	const minifiedSize = minified.length;
	const saved = originalSize - minifiedSize;
	return Math.round((saved / originalSize) * 100);
};

/**
 * Format file size in human readable format
 * @param bytes - Size in bytes
 * @returns Formatted size string
 */
export const formatFileSize = (bytes: number): string => {
	if (bytes === 0) return '0 B';
	const k = 1024;
	const sizes = ['B', 'KB', 'MB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};
