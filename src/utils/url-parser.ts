import { headers } from 'next/headers';

export const urlParser = async (
	{ isMultiLan }: { isMultiLan: boolean } = { isMultiLan: true }
) => {
	const headersList = await headers();
	const domain = headersList.get('host') || '';
	const fullUrl = headersList.get('referer') || '';
	return {
		domain,
		fullUrl,
		pathName: fullUrl.split(domain)[1]?.slice(isMultiLan ? 1 : 0),
	};
};

export function extractTitleFromPathName(hostname: string): string {
	try {
		// Loại bỏ protocol (http://, https://) nếu có
		const host = hostname.replace(/^https?:\/\//, '');
		// Loại bỏ đường dẫn và tham số query (nếu có)
		const list = host.split('/');

		if (list.length < 1) return 'Website';

		// Nếu không có subdomain, lấy domain chính (phần trước TLD)
		return capitalizeFirstLetter(list[list.length - 1].replace(/-/g, ' '));
	} catch (error) {
		return 'Website';
	}
}

export function capitalizeFirstLetter(string: string): string {
	return string.charAt(0).toUpperCase() + string.slice(1);
}

export interface TrackingInfo {
	prefix: string | null;
	trackingId: string | null;
}

export function parseTrackingId(trackingId: string | null): TrackingInfo {
	if (!trackingId) return { prefix: null, trackingId: null };

	// Find the position of the hyphen that separates prefix from ID
	const hyphenIndex = trackingId.indexOf('-');

	// If there's no hyphen, treat the whole string as the ID
	if (hyphenIndex === -1) return { prefix: null, trackingId: trackingId };

	// Extract the prefix (before the hyphen)
	const prefix = trackingId.substring(0, hyphenIndex);

	// Extract the ID (after the hyphen)
	const id = trackingId.substring(hyphenIndex + 1);

	return { prefix, trackingId: id };
}

/**
 * Extracts the base domain and tracking ID from a domain string
 * Format: "mediaforce-trk-id.dub.direct" -> { host: "mediaforce.dub.direct", trackingId: "trk-id" }
 *
 * @param domain - The domain string containing a tracking ID
 * @returns An object with the cleaned domain and the extracted tracking ID (or null if no tracking ID found)
 */
export function removeTrackingId(domain: string) {
	// Check if the domain is valid
	if (!domain) return { host: domain, trackingId: null, prefix: null };

	// Split the domain by dots to separate subdomains
	const parts = domain.split('.');

	// If we don't have at least 2 parts (subdomain.domain), return the original
	if (parts.length < 2) return { host: domain, trackingId: null, prefix: null };

	// Get the first part (subdomain with tracking ID)
	const firstPart = parts[0];

	// Find the position of the hyphen that separates the base name from the tracking ID
	const hyphenIndex = firstPart.indexOf('-');

	// If there's no hyphen, return the original domain with null tracking ID
	if (hyphenIndex === -1)
		return { host: domain, trackingId: null, prefix: null };

	// Extract the base subdomain name (before the hyphen)
	const baseSubdomain = firstPart.substring(0, hyphenIndex);

	// Extract the tracking ID (after the hyphen)
	const trackingId = firstPart.substring(hyphenIndex + 1);

	// Replace the first part with the base subdomain and rejoin
	parts[0] = baseSubdomain;
	const host = parts.join('.');

	return {
		host,
		...parseTrackingId(trackingId),
	};
}

/**
 * Adds a tracking ID to a domain by inserting it after the first subdomain
 * Format: "mediaforce.dub.direct" + "trk-id" -> "mediaforce-trk-id.dub.direct"
 *
 * @param domain - The original domain string
 * @param trackingId - The tracking ID to add (optional)
 * @returns The domain with the tracking ID added
 */
export function addTrackingId(domain: string, trackingId?: string): string {
	// Check if the domain is valid
	if (!domain) return domain;

	// If no tracking ID provided, return the original domain
	if (!trackingId) return domain;

	// Split the domain by dots to separate subdomains
	const parts = domain.split('.');

	// If we don't have at least 2 parts (subdomain.domain), return the original
	if (parts.length < 2) return domain;

	// Get the first part (subdomain)
	const firstPart = parts[0];

	// Create the new subdomain with tracking ID
	// Replace the first part with the new subdomain and rejoin
	parts[0] = `${firstPart}-${trackingId}`;

	return parts.join('.');
}

/**
 * Intelligently checks if a string is a valid URL and prepends "https://" if needed.
 * @param input - The string to check and potentially transform into a valid URL
 * @returns A properly formatted URL string
 */
export function ensureValidUrl(input: string): string {
	// Trim whitespace
	let url = input.trim();

	// If empty, return empty string
	if (!url) return '';

	// Check if it already has a protocol (http://, https://, ftp://, etc.)
	const hasProtocol = /^[a-z][a-z0-9+.-]*:\/\//i.test(url);

	if (!hasProtocol) {
		// Check if it starts with "www." or contains a dot followed by a domain extension
		const looksLikeUrl = /^www\.|(\.[a-z]{2,})/i.test(url);

		if (looksLikeUrl) {
			// Prepend https:// to URLs that look valid but don't have a protocol
			url = `https://${url}`;
		}
	}

	try {
		// Attempt to construct a URL object to validate
		new URL(url);
		return url;
	} catch (error) {
		// If URL construction fails but input has domain-like structure,
		// make a best effort to create a valid URL
		if (url.includes('.') && !url.includes(' ')) {
			return `https://${url}`;
		}

		// If it's clearly not a URL, return the original input
		return input;
	}
}
