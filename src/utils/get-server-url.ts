import { headers } from 'next/headers';

export const getServerUrl = async (
	{ isMultiLan }: { isMultiLan: boolean } = { isMultiLan: false }
) => {
	const headersList = await headers();
	const domain = headersList.get('host') || '';
	const fullUrl =
		headersList.get('referer') ||
		headersList.get('x-forwarded-host') ||
		headersList.get('x-pathname') ||
		'';
	return {
		domain,
		fullUrl,
		pathName: fullUrl.split(domain)[1]?.slice(isMultiLan ? 1 : 0),
	};
};
