/**
 * Interface defining the structure of parsed URL components
 */
export interface UrlComponents {
	protocol: string;
	domain: string;
	port: string;
}

/**
 * Parses a URL into its main components: protocol, domain and port
 * Automatically detects default ports (80 for HTTP, 443 for HTTPS) when not explicitly specified
 *
 * @param {string} url - The full URL to parse
 * @returns {UrlComponents} Object containing URL components
 *
 * @example
 * // Returns { protocol: "https", domain: "localhost", port: "443" }
 * parseUrlComponents("https://localhost/any/path");
 *
 * @example
 * // Returns { protocol: "http", domain: "example.com", port: "8080" }
 * parseUrlComponents("http://example.com:8080/some/path");
 */
export function extractUrlParts(url: string): UrlComponents {
	// Default values if parsing fails
	const defaultResult: UrlComponents = {
		protocol: 'https',
		domain: url,
		port: '80',
	};

	if (!url || typeof url !== 'string') {
		return defaultResult;
	}

	try {
		// Use URL API for robust parsing
		const urlObj = new URL(url);
		const protocol = urlObj.protocol.replace(':', '');

		// Determine port - if not explicitly specified, use default based on protocol
		let port = urlObj.port;
		if (!port) {
			// Set default port based on protocol
			if (protocol === 'http') {
				port = '80';
			} else if (protocol === 'https') {
				port = '443';
			}
		}

		return {
			protocol,
			domain: urlObj.hostname,
			port,
		};
	} catch (error) {
		// Fallback to regex if URL API fails
		try {
			// Regular expression to extract protocol, domain and port
			const urlRegex = /^(https?):\/\/([^:\/]+)(?::(\d+))?/i;
			const match = url.match(urlRegex);

			if (!match) {
				return defaultResult;
			}

			const protocol = match[1] || '';
			const domain = match[2] || '';

			// Determine port - if not explicitly specified, use default based on protocol
			let port = match[3] || '';
			if (!port) {
				// Set default port based on protocol
				if (protocol.toLowerCase() === 'http') {
					port = '80';
				} else if (protocol.toLowerCase() === 'https') {
					port = '443';
				}
			}

			return {
				protocol,
				domain,
				port,
			};
		} catch (regexError) {
			console.error('Error parsing URL:', regexError);
			return defaultResult;
		}
	}
}

/**
 * Formats a URL from its components
 *
 * @param {UrlComponents} components - The URL components
 * @returns {string} The formatted URL
 */
export function formatUrl(components: UrlComponents): string {
	const { protocol, domain, port } = components;

	// Skip standard ports in the URL string (80 for http, 443 for https)
	const includePort =
		port &&
		!(
			(protocol === 'http' && port === '80') ||
			(protocol === 'https' && port === '443')
		);

	return `${protocol}://${domain}${includePort ? `:${port}` : ''}`;
}
