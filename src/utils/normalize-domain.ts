/**
 * Normalize and validate domain names with unlimited subdomains
 * @param input - The input domain or URL to normalize
 * @returns Normalized HTTPS URL or throws an error
 */
export function normalizeDomain(input: string): string {
	// Trim whitespace
	const trimmedInput = input.trim();

	// Regular expression to validate domain format with unlimited subdomains
	// Handles URLs with paths and query parameters
	const domainRegex = /^(?:https?:\/\/)?([a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*\.[a-zA-Z]{2,})(?:\/.*)?$/;

	// Match the input against the regex
	const match = trimmedInput.match(domainRegex);

	// If no match, throw an error
	if (!match) {
		throw new Error('Invalid domain format');
	}

	// Extract the domain from the match
	const domain = match[1];

	// Return normalized HTTPS URL
	return `https://${domain}`;
}

/**
 * Safely normalize domain with error handling
 * @param input - The input domain or URL to normalize
 * @returns Normalized HTTPS URL or null if invalid
 */
export function safeDomainNormalize(input: string): string | null {
	try {
		return normalizeDomain(input);
	} catch (error) {
		console.error(error);
		return null;
	}
}
