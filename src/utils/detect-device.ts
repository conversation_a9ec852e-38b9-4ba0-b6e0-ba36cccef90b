export enum DeviceOS {
	WINDOWS = 'windows',
	MACOS = 'macos',
	LINUX = 'linux',
	IOS = 'ios',
	ANDROID = 'android',
	BLACKBERRY = 'blackberry',
	POSTMAN = 'postman',
	UNKNOWN = 'unknown',
}

interface Device {
	isDesktop: boolean;
	isMobile: boolean;
	isTablet: boolean;
	type: string;
	os: DeviceOS;
}

export function detectDevice(userAgent: string): Device {
	const ua = userAgent.toLowerCase();

	const deviceInfo: Device = {
		isDesktop: false,
		isMobile: false,
		isTablet: false,
		type: 'unknown',
		os: DeviceOS.UNKNOWN,
	};

	if (/postmanruntime/.test(ua)) {
		deviceInfo.type = 'tool';
		deviceInfo.os = DeviceOS.POSTMAN;
		return deviceInfo;
	}

	if (/android/.test(ua)) {
		deviceInfo.type = 'android';
		deviceInfo.os = DeviceOS.ANDROID;
		if (/mobile/.test(ua)) deviceInfo.isMobile = true;
		else deviceInfo.isTablet = true;
	} else if (/iphone|ipad|ipod/.test(ua)) {
		deviceInfo.type = 'ios';
		deviceInfo.os = DeviceOS.IOS;
		if (/ipad/.test(ua)) deviceInfo.isTablet = true;
		else deviceInfo.isMobile = true;
	} else if (/blackberry|bb10/.test(ua)) {
		deviceInfo.type = 'blackberry';
		deviceInfo.os = DeviceOS.BLACKBERRY;
		deviceInfo.isMobile = true;
	} else if (/windows phone/.test(ua)) {
		deviceInfo.type = 'windows';
		deviceInfo.os = DeviceOS.WINDOWS;
		deviceInfo.isMobile = true;
	} else if (/windows|macintosh|linux/.test(ua)) {
		deviceInfo.isDesktop = true;
		deviceInfo.type = 'desktop';
		if (/windows/.test(ua)) deviceInfo.os = DeviceOS.WINDOWS;
		else if (/macintosh/.test(ua)) deviceInfo.os = DeviceOS.MACOS;
		else if (/linux/.test(ua)) deviceInfo.os = DeviceOS.LINUX;
	}

	return deviceInfo;
}
