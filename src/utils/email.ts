/**
 * Generates a random unique email address using timestamp and random characters
 * @param {string} domain - The domain name for the email (default: "example.com")
 * @param {string} prefix - Optional prefix for the email username
 * @returns {string} A unique email address
 */
export function generateUniqueEmail(
	domain: string = 'example.com',
	prefix: string = ''
): string {
	// Get current timestamp in milliseconds
	const timestamp = Date.now();

	// Convert timestamp to base36 (alphanumeric) to make it shorter
	const timestampHash = timestamp.toString(36);

	// Generate 8 random alphanumeric characters
	const randomChars = Array(8)
		.fill(0)
		.map(() => {
			const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
			return chars.charAt(Math.floor(Math.random() * chars.length));
		})
		.join('');

	// Create username with optional prefix, timestamp hash, and random chars
	const username = prefix
		? `${prefix}_${timestampHash}_${randomChars}`
		: `user_${timestampHash}_${randomChars}`;

	// Return complete email address
	return `${username}@${domain}`;
}

/**
 * Generates multiple unique email addresses
 * @param {number} count - Number of unique emails to generate
 * @param {string} domain - The domain name for the emails
 * @param {string} prefix - Optional prefix for the email usernames
 * @returns {string[]} Array of unique email addresses
 */
export function generateMultipleUniqueEmails(
	count: number,
	domain = 'example.com',
	prefix = ''
): string[] {
	const emails: string[] = [];

	for (let i = 0; i < count; i++) {
		// Add small delay to ensure different timestamps even in fast loops
		const email = generateUniqueEmail(domain, prefix);
		emails.push(email);

		// If generating a lot of emails, adding a tiny delay helps ensure uniqueness
		if (count > 100) {
			const startTime = Date.now();
			while (Date.now() - startTime < 1) {
				// Tiny delay loop
			}
		}
	}

	return emails;
}
