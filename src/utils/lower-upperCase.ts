/**
 * Converts the first character of a string to lowercase.
 *
 * @param {string} sentence - The input string to process.
 * @returns {string} The modified string with the first character in lowercase.
 * If the input is not a valid string, it returns the input unchanged.
 *
 * @example
 * lowercaseFirstLetter("Hello World!");
 * // Returns: "hello World!"
 *
 * @example
 * lowercaseFirstLetter("");
 * // Returns: ""
 *
 * @example
 * lowercaseFirstLetter("123ABC");
 * // Returns: "123ABC"
 */
export function lowercaseFirstLetter(sentence: string | undefined): string {
	if (!sentence || typeof sentence !== 'string') {
		return sentence || ''; // Return unchanged if not a valid string
	}

	return sentence.charAt(0).toLowerCase() + sentence.slice(1);
}


/**
 * Converts the first character of a string to uppercase.
 *
 * @param {string} sentence - The input string to process.
 * @returns {string} The modified string with the first character in uppercase.
 * If the input is not a valid string, it returns the input unchanged.
 *
 * @example
 * uppercaseFirstLetter("hello world!");
 * // Returns: "Hello world!"
 *
 * @example
 * uppercaseFirstLetter("");
 * // Returns: ""
 *
 * @example
 * uppercaseFirstLetter("123abc");
 * // Returns: "123abc"
 */
export function uppercaseFirstLetter(sentence: string | undefined): string {
	if (!sentence || typeof sentence !== 'string') {
		return sentence || ''; // Return unchanged if not a valid string
	}

	return sentence.charAt(0).toUpperCase() + sentence.slice(1).toLowerCase();
}
