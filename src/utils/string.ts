export const upperCaseFirstLetter = (str: string) => {
	return str.charAt(0).toUpperCase() + str.slice(1);
};

export const randomStringFormList = (list: string[]) => {
	const randomIndex = Math.floor(Math.random() * list.length);
	return list[randomIndex];
}

export const randomStringFormListIndex = (
	list: string[],
	index?: number
): string => {
	if (!list.length) return ""; // phòng ngừa danh sách rỗng

	if (typeof index === "number") {
		const safeIndex = index % list.length;
		return list[safeIndex];
	}

	const randomIndex = Math.floor(Math.random() * list.length);
	return list[randomIndex];
};
