/**
 * Hàm retry generic xử lý các API call có thể thất bại
 *
 * @param fn - Hàm async cần retry
 * @param options - <PERSON><PERSON><PERSON> tùy chọn cấu hình
 * @returns Một promise trả về kết quả của hàm hoặc reject sau khi tất cả các lần thử thất bại
 */
export async function retryAsync<T>(
	fn: () => Promise<T | null>,
	options: {
		retries?: number; // Số lần thử lại tối đa
		delay?: number; // Độ trễ cơ bản giữa các lần thử lại (ms)
		backoffFactor?: number; // Hệ số tăng độ trễ mỗi lần thử lại
		validator?: (result: T | null) => boolean; // Hàm xác thực kết quả
		onRetry?: (attempt: number, error: Error | null, result: T | null) => void; // Callback khi retry
		onSuccess?: (result: T, count: number) => void; // Callback khi thành công
		onFailure?: (error: Error, lastResult: T | null) => T | null | void; // Callback khi tất cả retry thất bại
	} = {}
): Promise<T> {
	const {
		retries = 3,
		delay = 300,
		backoffFactor = 1,
		validator = (result) => result !== null,
		onRetry = () => {},
		onSuccess = () => {},
		onFailure,
	} = options;

	let lastError: Error | null = null;
	let lastResult: T | null = null;

	for (let attempt = 0; attempt <= retries; attempt++) {
		try {
			// Thực thi hàm
			const result = await fn();
			lastResult = result;
			// Nếu kết quả vượt qua xác thực, trả về nó
			if (validator(result)) {
				// Gọi callback thành công
				onSuccess(result as T, attempt);
				return result as T;
			}

			// Kết quả không vượt qua xác thực nhưng không throw
			lastError = new Error('Invalid result returned');
		} catch (error) {
			// Lưu lại error
			lastError = error instanceof Error ? error : new Error(String(error));
		}

		// Không trì hoãn ở lần thử cuối cùng
		if (attempt < retries) {
			// Tính độ trễ backoff
			const backoffDelay = delay * Math.pow(backoffFactor, attempt);

			// Thông báo về việc retry
			onRetry(attempt + 1, lastError, lastResult);

			// Đợi trước lần thử tiếp theo
			await new Promise((resolve) => setTimeout(resolve, backoffDelay));
		}
	}

	// Nếu chúng ta đến đây, tất cả các lần thử lại đã thất bại
	if (onFailure) {
		// Nếu có hàm onFailure, gọi nó với error cuối cùng và kết quả cuối cùng
		const fallbackResult = onFailure(
			lastError || new Error('All retries failed'),
			lastResult
		);

		// Nếu hàm onFailure trả về giá trị không phải null, sử dụng nó thay vì throw error
		if (fallbackResult !== null) {
			return fallbackResult as T;
		}
	}

	// Throw error nếu không có fallback hoặc fallback trả về null
	throw new Error(
		`Failed after ${retries} retries. Last error: ${lastError?.message || 'Unknown error'}`
	);
}
