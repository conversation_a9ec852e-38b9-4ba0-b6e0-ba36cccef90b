/**
 * Chia một mảng thành các lô nhỏ hơn
 * @param array Mảng cần chia
 * @param batchSize Kích thước của mỗi lô
 * @returns Mảng chứa các lô
 */
export function batchArray<T>(array: T[], batchSize: number): T[][] {
	if (!array.length) return [];

	const batches: T[][] = [];

	for (let i = 0; i < array.length; i += batchSize) {
		batches.push(array.slice(i, i + batchSize));
	}

	return batches;
}

/**
 * Xử lý một mảng theo lô với async callback
 * @param items Mảng các item cần xử lý
 * @param batchSize Kích thước của mỗi lô
 * @param callback Hàm xử lý từng lô
 * @returns Promise chứa mảng kết quả từ mỗi lô
 */
export async function processBatches<T, R>(
	items: T[],
	batchSize: number,
	callback: (batch: T[]) => Promise<R>
): Promise<R[]> {
	// Chia thành các lô
	const batches = batchArray(items, batchSize);
	const results: R[] = [];

	// Xử lý từng lô
	for (const batch of batches) {
		const result = await callback(batch);
		results.push(result);
	}

	return results;
}

/**
 * Xử lý một mảng theo lô với async callback (chạy song song)
 * @param items Mảng các item cần xử lý
 * @param batchSize Kích thước của mỗi lô
 * @param callback Hàm xử lý từng lô
 * @param concurrency Số lượng lô xử lý đồng thời (mặc định: 3)
 * @returns Promise chứa mảng kết quả từ mỗi lô
 */
export async function processBatchesParallel<T, R>(
	items: T[],
	batchSize: number,
	callback: (batch: T[], index: number) => Promise<R>,
	concurrency = 3
): Promise<R[]> {
	// Chia thành các lô
	const batches = batchArray(items, batchSize);
	const results: R[] = [];

	// Xử lý các lô theo concurrency
	for (let i = 0; i < batches.length; i += concurrency) {
		const currentBatches = batches.slice(i, i + concurrency);
		const batchPromises = currentBatches.map((batch, index) => callback(batch, index));

		const batchResults = await Promise.all(batchPromises);
		results.push(...batchResults);
	}

	return results;
}
