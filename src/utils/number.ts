// features/shared/utils/number-format.ts

/**
 * Format a number with abbreviation when it exceeds thresholds
 * @param value The numeric value to format
 * @param decimals Number of decimal places (default: 1)
 * @returns Formatted string with appropriate suffix (K, M, B, T)
 */
export function formatCompactNumber(
	value: number,
	decimals: number = 1
): string {
	// Handle edge cases
	if (value === null || value === undefined || isNaN(value)) {
		return '-';
	}

	// Define suffixes and their thresholds
	const suffixes = [
		{ value: 1e12, symbol: 'T' },  // Trillion
		{ value: 1e9, symbol: 'B' },   // Billion
		{ value: 1e6, symbol: 'M' },   // Million
		{ value: 1e3, symbol: 'K' }    // Thousand
	];

	// Check if the number should be abbreviated
	for (const { value: threshold, symbol } of suffixes) {
		if (Math.abs(value) >= threshold) {
			// Format with the appropriate suffix
			const formattedValue = (value / threshold).toFixed(decimals);
			// Remove trailing zeros after decimal point if decimals is > 0
			const cleanValue = decimals > 0
				? formattedValue.replace(/\.0+$/, '')
				: formattedValue;
			return `${cleanValue}${symbol}`;
		}
	}

	// Return the original number if below 1000
	return value.toString();
}

/**
 * Format a number with abbreviation and include currency symbol
 * @param value The numeric value to format
 * @param currency The currency code (default: 'USD')
 * @param decimals Number of decimal places (default: 1)
 * @returns Formatted string with currency symbol and appropriate suffix
 */
export function formatCompactCurrency(
	value: number,
	currency: string = 'USD',
	decimals: number = 1
): string {
	// Handle edge cases
	if (value === null || value === undefined || isNaN(value)) {
		return '-';
	}

	// Get currency symbol
	const currencySymbol = getCurrencySymbol(currency);

	// Format the number with the appropriate suffix
	const formattedNumber = formatCompactNumber(value, decimals);

	// Return the formatted currency
	return `${currencySymbol}${formattedNumber}`;
}

/**
 * Get currency symbol based on currency code
 * @param currency The currency code
 * @returns The currency symbol
 */
function getCurrencySymbol(currency: string): string {
	switch (currency.toUpperCase()) {
		case 'USD':
			return '$';
		case 'EUR':
			return '€';
		case 'GBP':
			return '£';
		case 'JPY':
			return '¥';
		case 'VND':
			return '₫';
		default:
			return currency + ' '; // If no symbol is found, use the currency code
	}
}

/**
 * Format a large number with commas as thousand separators
 * @param value The numeric value to format
 * @returns Formatted string with thousand separators
 */
export function formatNumberWithCommas(value: number): string {
	// Handle edge cases
	if (value === null || value === undefined || isNaN(value)) {
		return '-';
	}

	// Use Intl.NumberFormat for locale-aware formatting with thousand separators
	return new Intl.NumberFormat().format(value);
}
