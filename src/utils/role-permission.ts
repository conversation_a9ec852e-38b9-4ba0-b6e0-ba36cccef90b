import { TUserRole } from '@/features/users/data/schema';

const ROLE_LEVEL: Record<TUserRole, number> = {
	user: 1,
	manager: 2,
	admin: 3,
	super_admin: 4,
};

export type RoleCheckOptions = {
	equal?: boolean;
};

export function hasPermission(
	currentRole?: TUserRole,
	requiredRole?: TUserRole,
	options: RoleCheckOptions = {}
): boolean {
	if (!currentRole || !requiredRole) return false;

	const current = ROLE_LEVEL[currentRole];
	const required = ROLE_LEVEL[requiredRole];

	if (options.equal) return current === required;
	return current >= required;
}
