'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getAboutMe } from '@/services/auth/about-me';
import { usePathname, useRouter } from 'next/navigation';
import { getCookie, removeCookie } from '@/utils/cookies';
import { User } from '@/services/users/user';

interface AuthContextState {
	user?: User;
}

interface AuthContextActions {
	setUser: (user: User) => void;
}

const AuthContext = createContext<
	(AuthContextState & AuthContextActions) | undefined
>(undefined);

const AuthProvider = ({ children }: ChildrenProp) => {
	const [accessToken, setAccessToken] = useState<string>();
	const pathName = usePathname();
	const router = useRouter();
	const [user, setUser] = useState<User>();

	const { data, isSuccess, error } = useQuery({
		queryFn: getAboutMe,
		queryKey: ['login', accessToken],
		enabled: !!accessToken,
		retry: 1,
	});

	useEffect(() => {
		const token = getCookie('access_token');
		if (token) {
			setAccessToken(token);
		}
	}, []);

	useEffect(() => {
		if (isSuccess && data) {
			setUser(data);
			if (pathName === '/login') {
				router.replace('/dashboard');
			}
		}
	}, [isSuccess, data]);

	useEffect(() => {
		if (error) {
			removeCookie('access_token');
			removeCookie('refresh_token');
			if (pathName !== '/login') {
				router.replace('/login');
			}
		}
	}, [error]);

	return (
		<AuthContext.Provider
			value={{
				user,
				setUser,
			}}>
			{children}
		</AuthContext.Provider>
	);
};

const useAuth = () => {
	const context = useContext(AuthContext);
	if (context === undefined) {
		throw new Error('useAuth must be used within a AuthProvider');
	}
	return context;
};

export { useAuth, AuthProvider };
