'use client';

import { createContext, useContext, useEffect, useState } from 'react';

type Mode = 'dark' | 'light' | 'system';
type Theme = 'default' | 'red' | 'rose' | 'orange' | 'green' | 'blue' | 'yellow' | 'violet';

type ThemeProviderProps = {
	children: React.ReactNode;
	defaultMode?: Mode;
	defaultTheme?: Theme;
	storageKey?: string;
};

type ThemeProviderState = {
	mode: Mode;
	theme: Theme;
	setMode: (mode: Mode) => void;
	setTheme: (theme: Theme) => void;
};

const initialState: ThemeProviderState = {
	mode: 'system',
	theme: 'default',
	setMode: () => null,
	setTheme: () => null,
};

const ThemeProviderContext = createContext<ThemeProviderState>(initialState);

export function ThemeProvider({
	children,
	defaultMode = 'system',
	defaultTheme = 'default',
	storageKey = 'ui-theme',
	...props
}: ThemeProviderProps) {
	const [mode, _setMode] = useState<Mode>(defaultMode);
	const [theme, _setTheme] = useState<Theme>(defaultTheme);

	useEffect(() => {
		const storedMode = localStorage.getItem(`${storageKey}-mode`) as Mode | null;
		const storedTheme = localStorage.getItem(`${storageKey}-theme`) as Theme | null;

		if (storedMode) {
			_setMode(storedMode);
		}
		if (storedTheme) {
			_setTheme(storedTheme);
		}
	}, [storageKey]);

	useEffect(() => {
		const root = window.document.documentElement;
		const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

		const applyTheme = (mode: Mode, theme: Theme) => {
			// Remove existing classes
			root.classList.remove('light', 'dark', 'default', 'red', 'rose', 'orange', 'green', 'blue', 'yellow', 'violet');

			// Apply mode (dark/light)
			const systemMode = mediaQuery.matches ? 'dark' : 'light';
			const effectiveMode = mode === 'system' ? systemMode : mode;
			root.classList.add(effectiveMode);

			// Apply theme (default/red/rose/orange/...)
			if (theme !== 'default') {
				root.classList.add(theme);
			}
		};

		const handleChange = () => {
			if (mode === 'system') {
				applyTheme(mode, theme);
			}
		};

		applyTheme(mode, theme);

		mediaQuery.addEventListener('change', handleChange);

		return () => mediaQuery.removeEventListener('change', handleChange);
	}, [mode, theme]);

	const setMode = (mode: Mode) => {
		localStorage.setItem(`${storageKey}-mode`, mode);
		_setMode(mode);
	};

	const setTheme = (theme: Theme) => {
		localStorage.setItem(`${storageKey}-theme`, theme);
		_setTheme(theme);
	};

	const value = {
		mode,
		theme,
		setMode,
		setTheme,
	};

	return (
		<ThemeProviderContext.Provider {...props} value={value}>
			{children}
		</ThemeProviderContext.Provider>
	);
}

export const useTheme = () => {
	const context = useContext(ThemeProviderContext);

	if (context === undefined)
		throw new Error('useTheme must be used within a ThemeProvider');

	return context;
};
