'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { fonts } from '@/config/fonts'

type Font = (typeof fonts)[number]

interface FontContextType {
  font: Font
  setFont: (font: Font) => void
}

const FontContext = createContext<FontContextType | undefined>(undefined)

export const FontProvider: React.FC<{ children: React.ReactNode }> = ({
                                                                        children,
                                                                      }) => {
  // Sửa lỗi localStorage - initialize với fonts[0] và sau đó đọc từ localStorage trong useEffect
  const [font, _setFont] = useState<Font>(fonts[0])

  // Đọc font từ localStorage sau khi component mount ở phía client
  useEffect(() => {
    const savedFont = localStorage.getItem('font')
    if (savedFont && fonts.includes(savedFont as Font)) {
      _setFont(savedFont as Font)
    }
  }, [])

  useEffect(() => {
    const applyFont = (font: string) => {
      const root = document.documentElement
      root.classList.forEach((cls) => {
        if (cls.startsWith('font-')) root.classList.remove(cls)
      })
      root.classList.add(`font-${font}`)
    }

    applyFont(font)
  }, [font])

  const setFont = (font: Font) => {
    localStorage.setItem('font', font)
    _setFont(font)
  }

  // Sửa lỗi thiếu thuộc tính Provider - thay FontContext bằng FontContext.Provider
  return (
    <FontContext.Provider value={{ font, setFont }}>
      {children}
    </FontContext.Provider>
  )
}

export const useFont = () => {
  const context = useContext(FontContext)
  if (!context) {
    throw new Error('useFont must be used within a FontProvider')
  }
  return context
}
