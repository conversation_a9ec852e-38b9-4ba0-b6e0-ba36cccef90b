'use client';
import Typography from '@/components/shared/Typography';
import { FaTelegramPlane } from 'react-icons/fa';
import Logo from '@/assets/logo';
import Link from 'next/link';
import { useQuery } from '@tanstack/react-query';
import { loginUser } from '@/services/auth/login';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { setCookie } from '@/utils/cookies';
import { toast } from 'sonner';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';

export default function LoginContainer() {
	const urlQuery = useSearchParams();
	const router = useRouter();
	const token = urlQuery.get('token') as string | null;
	const { setUser } = useAuth();

	const { data, error, isSuccess } = useQuery({
		queryKey: ['login', token],
		queryFn: () => loginUser(token!),
		enabled: !!token,
		retry: 0,
	});

	useEffect(() => {
		if (isSuccess && data) {
			setCookie('access_token', data.auth.accessToken, {
				expires: 7,
				path: '/',
				secure: true,
				sameSite: 'Strict',
			});

			setCookie('refresh_token', data.auth.refreshToken, {
				expires: 7,
				path: '/',
				secure: true,
				sameSite: 'Strict',
			});
			setUser(data.data);
			router.replace('/dashboard');
		}
	}, [isSuccess, data]);

	useEffect(() => {
		if (!error) return;

		router.replace('/login');

		const err = error as ErrorResponse;
		const errorMessage = err?.response?.data?.message;

		if (errorMessage === 'Invalid token') {
			toast.error('Invalid token or token expired!', {
				description: 'Please try generating a new token',
			});
		} else {
			toast.error('Failed to login', {
				description: 'Please try again',
			});
		}
	}, [error, router, toast]);

	return (
		<div
			className={'flex w-full flex-auto flex-col items-center justify-center'}>
			<div
				className={
					'flex aspect-[24/9] w-48 items-center justify-center rounded-xl'
				}>
				<Logo
					opts={{
						iconSize: 72,
						text: {
							height: 56,
							width: 96,
						},
					}}
				/>
			</div>
			<div className={'py-6'}>
				<Typography
					tag={'h1'}
					variant={'20px_f500_l20'}
					className={'mb-1 text-center'}>
					Access Dashboard Now
				</Typography>
				<Typography>Link Telegram for instant dashboard use</Typography>
			</div>
			<div className={'flex w-full justify-center'}>
				<Link
					href={`https://t.me/${process.env.NEXT_PUBLIC_TELEGRAM_BOT}?start=_`}
					target={'_blank'}>
					<Button variant={'secondary'}>
						<FaTelegramPlane />
						Connect Telegram
					</Button>
				</Link>
			</div>
		</div>
	);
}
