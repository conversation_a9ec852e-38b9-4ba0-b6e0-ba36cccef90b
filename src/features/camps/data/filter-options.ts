import { Building, Calendar, Clock, Link } from 'lucide-react';

// Brand filter options (có thể lấy từ API hoặc hardcode)
export const brandOptions = [
	{ label: 'socialflyny', value: 'socialflyny', icon: Building },
	{ label: 'themany', value: 'themany', icon: Building },
	{ label: 'auth', value: 'auth', icon: Building },
	{ label: 'nauth', value: 'nauth', icon: Building },
	{ label: 'moburst', value: 'moburst', icon: Building },
	{ label: 'twotreesppc', value: 'twotreesppc', icon: Building },
] as const;

// Webhook URL filter options
export const webhookUrlOptions = [
	{ label: 'n8n.dub.direct', value: 'n8n.dub.direct', icon: Link },
	{ label: 'webhook.site', value: 'webhook.site', icon: Link },
	{ label: 'localhost', value: 'localhost', icon: Link },
] as const;

// Created date filter options
export const createdDateOptions = [
	{ label: 'Today', value: 'today', icon: Calendar },
	{ label: 'Yesterday', value: 'yesterday', icon: Calendar },
	{ label: 'Last 7 days', value: 'last7days', icon: Calendar },
	{ label: 'Last 30 days', value: 'last30days', icon: Calendar },
	{ label: 'This month', value: 'thismonth', icon: Calendar },
	{ label: 'Last month', value: 'lastmonth', icon: Calendar },
] as const;

// Status filter options
export const statusOptions = [
	{ label: 'Active', value: 'active', icon: Clock },
	{ label: 'Completed', value: 'completed', icon: Clock },
	{ label: 'Pending', value: 'pending', icon: Clock },
	{ label: 'Failed', value: 'failed', icon: Clock },
] as const;
