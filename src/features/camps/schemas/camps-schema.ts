import z from 'zod';

export const acceptedFileTypes = ['application/csv'];

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB tính bằng byte

export const campFormSchema = z.object({
	brand: z.string().min(1, 'Tên Brand là bắt buộc'),
	webhook: z.string().min(1, 'Webhook ID là bắt buộc'), // Changed from webhookUrl to webhook (ID)
	mail: z.string().min(1, 'Mail template là bắt buộc'), // Changed from optional mailTemplate to required mail
	file: z
		.instanceof(File)
		.refine((file) => acceptedFileTypes.includes(file.type), {
			message: 'Loại file không được hỗ trợ',
		})
		.refine((file) => file.size <= MAX_FILE_SIZE, {
			message: `File phải nhỏ hơn 10MB`,
		}),
	startDate: z.date(),
});

// Schema for editing camp (file is optional)
export const campEditSchema = z.object({
	brand: z.string().min(1, 'Tên Brand là bắt buộc'),
	webhook: z.string().min(1, 'Webhook ID là bắt buộc'), // Changed from webhookUrl to webhook (ID)
	mail: z.string().optional(), // Optional mail template ID for editing
	file: z
		.instanceof(File)
		.refine((file) => acceptedFileTypes.includes(file.type), {
			message: 'Loại file không được hỗ trợ',
		})
		.refine((file) => file.size <= MAX_FILE_SIZE, {
			message: `File phải nhỏ hơn 10MB`,
		})
		.optional(), // File is optional for editing
	startDate: z.date(),
});

// Schema for updating camp (all fields optional except ID)
export const campUpdateSchema = campFormSchema.partial().extend({
	_id: z.string().min(1, 'ID is required'),
});

// Complete camp schema with all database fields
export const campSchema = z.object({
	_id: z.string(),
	brand: z.string(),
	webhook: z.string(), // Changed from webhookUrl to webhook (ID)
	file: z.string(),
	startDate: z.string(),
	mail: z.object({
		_id: z.string(),
		sender: z.string(),
		senderName: z.string(),
		brand: z.string(),
		startHTML: z.object({
			subject: z.string(),
			content: z.string(),
			delay: z.number(),
		}),
		otherHTML: z.array(z.object({
			subject: z.string(),
			content: z.string(),
			delay: z.number(),
		})),
		endHTML: z.object({
			subject: z.string(),
			content: z.string(),
			delay: z.number(),
		}),
		createdAt: z.string(),
		createdBy: z.string(),
		updatedAt: z.string().optional(),
		updatedBy: z.string().optional(),
	}).optional(),
	createdAt: z.string(),
	createdBy: z.string(),
	updatedAt: z.string().optional(),
	updatedBy: z.string().optional(),
});

export type TCampForm = z.infer<typeof campFormSchema>;
export type TCampEdit = z.infer<typeof campEditSchema>;
export type TCampUpdate = z.infer<typeof campUpdateSchema>;
export type TCamp = z.infer<typeof campSchema>;
