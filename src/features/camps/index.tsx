'use client';
import { getCamps } from '@/services/camp/get-camps';
import { useQuery } from '@tanstack/react-query';
import { useSearchParams } from 'next/navigation';
import { CampCardList } from '@/features/camps/CampCardList';
import { ListPagination } from '@/components/shared/Panigation/ListPagination';
import { ThemeSwitch } from '@/components/theme-switch';
import { ProfileDropdown } from '@/components/profile-dropdown';
import { Header } from '@/components/layout/header';
import { Main } from '@/components/layout/main';
import { CampFiltersComponent, CampFilters } from '@/features/camps/components/CampFilters';
import { FilterSummary } from '@/features/camps/components/FilterSummary';
import { useState, useMemo } from 'react';

const PAGE_LIMIT_ITEM = 10;

export const CampContainer = () => {
	const searchParams = useSearchParams();
	const page = Number(searchParams.get('page')) || 1;
	const skip = (page - 1) * PAGE_LIMIT_ITEM;

	// Filter state
	const [filters, setFilters] = useState<CampFilters>({
		search: '',
		brand: '',
		startDate: '',
	});

	// Fetch camps with filters
	const { data, isPending } = useQuery({
		queryKey: ['camps', page, skip, filters],
		queryFn: () => getCamps({
			skip,
			limit: PAGE_LIMIT_ITEM,
			search: filters.search || undefined,
			brand: filters.brand || undefined,
			startDate: filters.startDate || undefined,
		}),
	});

	// Client-side filtering for additional filters that might not be supported by API
	const filteredData = useMemo(() => {
		if (!data) return null;

		let filtered = data.data;

		// Apply client-side search if needed
		if (filters.search) {
			const searchLower = filters.search.toLowerCase();
			filtered = filtered.filter((camp) => {
				// Handle webhook field (can be object or string)
				const webhookText = typeof camp.webhook === 'object'
					? camp.webhook?._id || ''
					: camp.webhook || '';

				return (
					camp.brand.toLowerCase().includes(searchLower) ||
					webhookText.toLowerCase().includes(searchLower)
				);
			});
		}

		return {
			...data,
			data: filtered,
			total: filtered.length,
		};
	}, [data, filters]);

	// Reset filters
	const resetFilters = () => {
		setFilters({
			search: '',
			brand: '',
			startDate: '',
		});
	};

	return (
		<>
			<Header fixed>
				<div className="ml-auto flex items-center space-x-4">
					<ThemeSwitch />
					<ProfileDropdown />
				</div>
			</Header>
			<Main>
				<div className="space-y-6">
					{/* Filters and Create Button */}
					<div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
						<div className="flex-1 w-full">
							<CampFiltersComponent
								filters={filters}
								onFiltersChange={setFilters}
								onReset={resetFilters}
							/>
						</div>
					</div>
					{/* Results */}
					<div>
						{isPending ? (
							<div className="flex items-center justify-center h-64">
								<p>Loading camps...</p>
							</div>
						) : filteredData ? (
							<div className="space-y-4">
								{filteredData.data.length > 0 ? (
									<>
										<FilterSummary
											filters={filters}
											totalResults={data?.total || 0}
											filteredResults={filteredData.data.length}
										/>
										<CampCardList data={filteredData.data} />
										<ListPagination
											totalPages={Math.ceil((data?.total || 0) / PAGE_LIMIT_ITEM)}
											currentPage={page}
										/>
									</>
								) : (
									<div className="flex flex-col items-center justify-center h-64 text-center">
										<p className="text-lg font-medium text-gray-600 dark:text-gray-400">
											No camps found
										</p>
										<p className="text-sm text-gray-500 dark:text-gray-500">
											Try adjusting your filters or create a new camp
										</p>
									</div>
								)}
							</div>
						) : (
							<div className="flex items-center justify-center h-64">
								<p className="text-red-500">Error loading camps</p>
							</div>
						)}
					</div>
				</div>
			</Main>
		</>
	);
};
