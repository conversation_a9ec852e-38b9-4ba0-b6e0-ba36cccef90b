import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { BiSearch } from 'react-icons/bi';
import { Filter, X } from 'lucide-react';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { brandOptions } from '@/features/camps/data/filter-options';
import { CreateCampDialog } from '@/features/camps/create/CreateCampDialog';

export interface CampFilters {
	search: string;
	brand: string;
	startDate: string;
}

interface CampFiltersProps {
	filters: CampFilters;
	onFiltersChange: (filters: CampFilters) => void;
	onReset: () => void;
}

export const CampFiltersComponent = ({
	filters,
	onFiltersChange,
	onReset,
}: CampFiltersProps) => {
	const [showAdvanced, setShowAdvanced] = useState(false);

	const updateFilter = (key: keyof CampFilters, value: string) => {
		onFiltersChange({
			...filters,
			[key]: value,
		});
	};

	const hasActiveFilters = Object.entries(filters).some(
		([key, value]) => key !== 'search' && value !== ''
	);

	const getActiveFilterCount = () => {
		return Object.entries(filters).filter(
			([key, value]) => key !== 'search' && value !== ''
		).length;
	};

	return (
		<div className="space-y-4">
			{/* Search and Toggle Row */}
			<div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
				<div className="flex flex-1 items-center gap-2">
					<Input
						type="search"
						placeholder="Search camps..."
						containerClassName="min-w-96 w-fit"
						icon={<BiSearch className="size-5" />}
						value={filters.search}
						onChange={(e) => updateFilter('search', e.target.value)}
					/>

					<Button
						variant="outline"
						size="sm"
						onClick={() => setShowAdvanced(!showAdvanced)}
						className="flex h-11 items-center gap-2">
						<Filter className="size-4" />
						Filters
						{getActiveFilterCount() > 0 && (
							<Badge variant="secondary" className="ml-1">
								{getActiveFilterCount()}
							</Badge>
						)}
					</Button>

					{hasActiveFilters && (
						<Button
							variant="ghost"
							size="sm"
							onClick={onReset}
							className="flex h-11 items-center gap-2 text-gray-500 hover:text-gray-700">
							<X className="size-4" />
							Clear
						</Button>
					)}
				</div>

				<CreateCampDialog />
			</div>

			{/* Advanced Filters */}
			{showAdvanced && (
				<div className="bg-muted/50 space-y-6 rounded-lg p-4">
					{/* Detailed Filters */}
					<div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
						{/* Brand Filter */}
						<div className="space-y-2">
							<label className="text-sm font-medium text-gray-700 dark:text-gray-300">
								Brand
							</label>
							<Select
								value={filters.brand || 'all'}
								onValueChange={(value) =>
									updateFilter('brand', value === 'all' ? '' : value)
								}>
								<SelectTrigger className={'w-full'}>
									<SelectValue placeholder="All brands" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">All brands</SelectItem>
									{brandOptions.map((option) => (
										<SelectItem key={option.value} value={option.value}>
											<div className="flex items-center gap-2">
												{option.icon && <option.icon className="size-4" />}
												{option.label}
											</div>
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						{/* Start Date Filter */}
						<div className="space-y-2">
							<label className="text-sm font-medium text-gray-700 dark:text-gray-300">
								Start Date
							</label>
							<Input
								type="date"
								placeholder="Start date..."
								value={filters.startDate}
								onChange={(e) => updateFilter('startDate', e.target.value)}
							/>
						</div>
					</div>

					{/* Active Filters Display */}
					{hasActiveFilters && (
						<div className="flex flex-wrap gap-2">
							{filters.brand && (
								<Badge variant="secondary" className="flex items-center gap-1">
									Brand:{' '}
									{brandOptions.find((o) => o.value === filters.brand)?.label}
									<X
										className="size-3 cursor-pointer"
										onClick={() => updateFilter('brand', '')}
									/>
								</Badge>
							)}
							{filters.startDate && (
								<Badge variant="secondary" className="flex items-center gap-1">
									Start Date: {filters.startDate}
									<X
										className="size-3 cursor-pointer"
										onClick={() => updateFilter('startDate', '')}
									/>
								</Badge>
							)}
						</div>
					)}
				</div>
			)}
		</div>
	);
};
