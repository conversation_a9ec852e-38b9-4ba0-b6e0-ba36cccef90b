import { Camp } from '@/types/camp';
import { format } from 'date-fns';
import { Building, Calendar, Clock, Eye, Mail } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Typography from '@/components/shared/Typography';
import { Badge } from '@/components/ui/badge';
import { StartCampDialog } from './StartCampDialog';
import { EditCampDialog } from './EditCampDialog';

interface CampCardProps {
	camp: Camp;
}

export const CampCard = ({ camp }: CampCardProps) => {
	const router = useRouter();

	return (
		<Card className="transition-shadow hover:shadow-md">
			<CardContent className="pt-6">
				<div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
					{/* Brand & Basic Info */}
					<div className="space-y-2">
						<div className="flex items-center gap-2">
							<Building className="size-4 text-blue-500" />
							<Typography variant="14px_f600_l21" className="text-blue-600">
								{camp.brand}
							</Typography>
						</div>
						<Typography
							variant="12px_f400_l18"
							className="truncate text-gray-600">
							Webhook ID:{' '}
							{typeof camp.webhook === 'object'
								? camp.webhook?._id || 'Not assigned'
								: camp.webhook || 'Not assigned'}
						</Typography>
					</div>

					{/* Mail Template Info */}
					<div className="space-y-2">
						<div className="flex items-center gap-2">
							<Mail className="size-4 text-green-500" />
							<Typography variant="12px_f500_l16" className="text-green-600">
								Mail Template
							</Typography>
						</div>
						{camp.mail ? (
							<Typography variant="12px_f400_l18" className="text-gray-600">
								Mail ID: {camp.mail}
							</Typography>
						) : (
							<Typography variant="12px_f400_l18" className="text-gray-500">
								Not assigned
							</Typography>
						)}
					</div>

					{/* Timing Info */}
					<div className="space-y-2">
						<div className="flex items-center gap-2">
							<Clock className="size-4 text-orange-500" />
							<Typography variant="12px_f500_l16" className="text-orange-600">
								Schedule
							</Typography>
						</div>
						<Typography variant="12px_f400_l18" className="text-gray-600">
							{format(new Date(camp.startDate), 'HH:mm dd/MM/yyyy')}
						</Typography>
					</div>

					{/* Meta Info */}
					<div className="space-y-2">
						<div className="flex items-center gap-2">
							<Calendar className="size-4 text-purple-500" />
							<Typography variant="12px_f500_l16" className="text-purple-600">
								Created
							</Typography>
						</div>
						<Typography variant="12px_f400_l18" className="text-gray-600">
							{format(new Date(camp.createdAt), 'dd/MM/yyyy')}
						</Typography>
						<Typography variant="12px_f400_l18" className="text-gray-500">
							By: {camp.createdBy}
						</Typography>
					</div>
				</div>
			</CardContent>
			<CardFooter className="flex items-center justify-between pt-4">
				<div className="flex gap-2">
					<Button
						variant="outline"
						size="sm"
						onClick={() => router.push(`camps/${camp._id}`)}
						title="Xem chi tiết">
						<Eye className="mr-1 size-4" />
						View
					</Button>
					<EditCampDialog camp={camp} variant="outline" size="sm" />
					<StartCampDialog
						camp={camp}
						variant="default"
						size="sm"
						className="bg-green-600 hover:bg-green-700"
					/>
				</div>
				<Badge variant="secondary" className="text-xs">
					ID: {camp._id.slice(-8)}
				</Badge>
			</CardFooter>
		</Card>
	);
};
