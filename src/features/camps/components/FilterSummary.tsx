'use client';

import { Badge } from '@/components/ui/badge';
import { CampFilters } from '@/features/camps/components/CampFilters';
import {
	brandOptions,
	createdDateOptions,
} from '@/features/camps/data/filter-options';

interface FilterSummaryProps {
	filters: CampFilters;
	totalResults: number;
	filteredResults: number;
}

export const FilterSummary = ({
	filters,
	totalResults,
	filteredResults,
}: FilterSummaryProps) => {
	const hasActiveFilters = Object.entries(filters).some(
		([key, value]) => key !== 'search' && value !== ''
	);

	const getFilterLabel = (key: keyof CampFilters, value: string): string => {
		switch (key) {
			case 'brand':
				return brandOptions.find((o) => o.value === value)?.label || value;
			case 'startDate':
				return (
					createdDateOptions.find((o) => o.value === value)?.label || value
				);
			default:
				return value;
		}
	};

	return (
		<div className="flex flex-col gap-3 rounded-lg border bg-gray-50 p-4 dark:bg-gray-900">
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-2">
					<span className="text-sm font-medium text-gray-700 dark:text-gray-300">
						Results:
					</span>
					<span className="text-sm text-gray-600 dark:text-gray-400">
						{filteredResults} of {totalResults} camps
					</span>
				</div>

				{hasActiveFilters && (
					<span className="text-xs text-gray-500">
						{Object.entries(filters).filter(([key, value]) => key !== 'search' && value !== '').length} filter(s) applied
					</span>
				)}
			</div>

			{(filters.search || hasActiveFilters) && (
				<div className="flex flex-wrap gap-2">
					{filters.search && (
						<Badge variant="outline" className="text-xs">
							Search: &#34;{filters.search}&#34;
						</Badge>
					)}
					{Object.entries(filters).map(([key, value]) => {
						if (key === 'search' || !value) return null;
						return (
							<Badge key={key} variant="secondary" className="text-xs">
								{key === 'mailTemplate'
									? 'Mail Template'
									: key === 'createdDate'
										? 'Date'
										: key.charAt(0).toUpperCase() + key.slice(1)}
								: {getFilterLabel(key as keyof CampFilters, value)}
							</Badge>
						);
					})}
				</div>
			)}
		</div>
	);
};
