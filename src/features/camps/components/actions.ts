'use server';
import { Camp, CampDetail } from '@/types/camp';

export const n8nTrigger = async (camp: Camp | CampDetail): Promise<boolean> => {
	try {
		let webhookUrl: string;

		// Check if camp has full webhook details (CampDetail) or just ID (Camp)
		if ('webhookUrl' in camp.webhook) {
			// CampDetail - webhook has full info
			webhookUrl = camp.webhook.webhookUrl;
		} else {
			// Camp - webhook only has _id, need to fetch details
			const { getWebhookDetail } = await import('@/services/webhook/get-webhook-detail');
			const webhook = await getWebhookDetail(camp.webhook._id);

			if (!webhook || !webhook.webhookUrl) {
				console.error('Webhook not found or missing webhookUrl');
				return false;
			}

			webhookUrl = webhook.webhookUrl;
		}

		const res = await fetch(webhookUrl, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				'api-key': process.env.N8N_API_KEY || '',
			},
			body: JSON.stringify({
				id: camp._id,
			}),
		});

		return res.ok;
	} catch (error) {
		console.error('Error triggering webhook:', error);
		return false;
	}
};
