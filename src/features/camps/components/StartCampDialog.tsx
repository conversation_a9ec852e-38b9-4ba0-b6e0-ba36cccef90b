'use client';

import { useState } from 'react';
import { Play } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Camp } from '@/types/camp';
import { n8nTrigger } from '@/features/camps/components/actions';
import { toast } from 'sonner';

interface StartCampDialogProps {
	camp: Camp;
	variant?:
		| 'default'
		| 'outline'
		| 'secondary'
		| 'ghost'
		| 'link'
		| 'destructive';
	size?: 'default' | 'sm' | 'lg' | 'icon';
	className?: string;
}

export const StartCampDialog = ({
	camp,
	variant = 'default',
	size = 'default',
	className,
}: StartCampDialogProps) => {
	const [open, setOpen] = useState(false);

	const handleStart = async () => {
		const res = await n8nTrigger(camp);
		if (res) {
			setOpen(false);
			toast.success('Campaign started successfully!');
		} else {
			toast.error('Failed to start campaign. Please try again.');
		}
	};

	return (
		<AlertDialog open={open} onOpenChange={setOpen}>
			<AlertDialogTrigger asChild>
				<Button variant={variant} size={size} className={className}>
					<Play className="mr-2 size-4" />
					Start
				</Button>
			</AlertDialogTrigger>
			<AlertDialogContent>
				<AlertDialogHeader>
					<AlertDialogTitle>Start Campaign</AlertDialogTitle>
					<AlertDialogDescription>
						Are you sure you want to start the campaign for{' '}
						<strong>{camp.brand}</strong>? This action will trigger the webhook
						and begin the campaign process.
					</AlertDialogDescription>
				</AlertDialogHeader>
				<AlertDialogFooter>
					<AlertDialogCancel>Cancel</AlertDialogCancel>
					<AlertDialogAction onClick={handleStart}>
						Start Campaign
					</AlertDialogAction>
				</AlertDialogFooter>
			</AlertDialogContent>
		</AlertDialog>
	);
};
