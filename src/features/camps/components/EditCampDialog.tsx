'use client';

import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Edit } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from '@/components/ui/dialog';
import { Camp, CampDetail } from '@/types/camp';
import { CampForm } from '../create/CampForm';
import { updateCamp } from '@/services/camp/update-camp';
import { toast } from 'sonner';
import { campEditSchema, TCampEdit } from '../schemas/camps-schema';

interface EditCampDialogProps {
	camp: Camp | CampDetail;
	variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
	size?: 'default' | 'sm' | 'lg' | 'icon';
	className?: string;
}

export const EditCampDialog = ({
	camp,
	variant = 'outline',
	size = 'sm',
	className
}: EditCampDialogProps) => {
	const [open, setOpen] = useState(false);
	const queryClient = useQueryClient();

	// Convert camp data to form format
	const defaultValues: Partial<TCampEdit> = {
		brand: camp.brand,
		webhook: camp.webhook._id,
		mail: typeof camp.mail === 'object' ? camp.mail._id : camp.mail,
		startDate: new Date(camp.startDate),
		// Note: file cannot be pre-filled in forms, user needs to re-upload
	};

	const form = useForm<TCampEdit>({
		resolver: zodResolver(campEditSchema),
		defaultValues,
	});

	const mutation = useMutation({
		mutationFn: (data: TCampEdit) => {
			// Convert form data to update format (exclude file if not provided)
			const updateData: any = {
				brand: data.brand,
				webhook: data.webhook,
				mail: data.mail,
				startDate: data.startDate.toISOString(),
			};

			// Only include file if a new one is uploaded
			if (data.file) {
				updateData.file = data.file;
			}

			return updateCamp(camp._id, updateData);
		},
		onSuccess: async () => {
			toast.success('Camp updated successfully!');
			await queryClient.invalidateQueries({ queryKey: ['camps'] });
			await queryClient.invalidateQueries({ queryKey: ['camp', camp._id] });
			setOpen(false);
			form.reset();
		},
		onError: (error: Error) => {
			toast.error(`Failed to update camp: ${error.message}`);
		},
	});

	const handleSubmit = (data: TCampEdit) => {
		mutation.mutate(data);
	};

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger asChild>
				<Button
					variant={variant}
					size={size}
					className={className}
					disabled={mutation.isPending}
					title="Chỉnh sửa"
				>
					<Edit className="mr-1 size-4" />
					Edit
				</Button>
			</DialogTrigger>
			<DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle>Edit Camp</DialogTitle>
					<DialogDescription>
						Update camp information. Note: You need to re-upload the file if you want to change it.
					</DialogDescription>
				</DialogHeader>
				<Form {...form}>
					<form onSubmit={form.handleSubmit(handleSubmit)}>
						<CampForm
							isLoading={mutation.isPending}
							submitButtonText="Update Camp"
							isEditMode={true}
						/>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
};
