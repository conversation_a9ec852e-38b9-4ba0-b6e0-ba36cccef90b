import { useFormContext } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Calendar } from '@/components/ui/calendar';
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar1, Mail } from 'lucide-react';
import { format, subDays } from 'date-fns';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import Typography from '@/components/shared/Typography';
import { TCampEdit, TCampForm } from '@/features/camps/schemas/camps-schema';
import { InputField } from '@/components/input-field';
import { useQuery } from '@tanstack/react-query';
import { getMailTemplates } from '@/services/camp';
import { getWebhooks } from '@/services/webhook';
import { useEffect, useState } from 'react';

interface CampFormProps {
	onSubmit?: (data: TCampForm | TCampEdit) => void;
	defaultValues?: Partial<TCampForm | TCampEdit>;
	isLoading?: boolean;
	submitButtonText?: string;
	isEditMode?: boolean;
}

export const CampForm = ({
	isLoading = false,
	submitButtonText = 'Thêm',
	isEditMode = false,
}: CampFormProps) => {
	const form = useFormContext<TCampForm | TCampEdit>();
	const [selectedBrand, setSelectedBrand] = useState<string>('');

	// Watch brand field to fetch mail templates
	const brandValue = form.watch('brand');

	// Fetch webhooks
	const { data: webhooksResponse, isLoading: isLoadingWebhooks } = useQuery({
		queryKey: ['webhooks'],
		queryFn: () => getWebhooks({ limit: 100 }),
	});

	const webhooks = webhooksResponse?.data || [];

	// Fetch mail templates when brand changes
	const { data: mailTemplatesResponse, isLoading: isLoadingTemplates } =
		useQuery({
			queryKey: ['mail-templates-by-brand', brandValue],
			queryFn: () => getMailTemplates({ brand: brandValue, limit: 100 }),
			enabled: !!brandValue && brandValue.length > 0,
		});

	const mailTemplates = mailTemplatesResponse?.data || [];

	// Reset mail template when brand changes
	useEffect(() => {
		if (brandValue !== selectedBrand) {
			setSelectedBrand(brandValue);
			form.setValue('mail', '');
		}
	}, [brandValue, selectedBrand, form]);

	return (
		<div
			className={'border-natural-6 space-y-4 rounded-2xl border p-5 shadow-sm'}>
			<InputField
				form={form}
				name="brand"
				label="Brand"
				placeholder="Enter brand..."
				required
			/>

			{/* Webhook Selector */}
			<div className="space-y-3">
				<Typography>
					Webhook <span className="text-red-500">*</span>
				</Typography>
				<FormField
					control={form.control}
					name="webhook"
					render={({ field }) => (
						<FormItem>
							<FormControl>
								<Select
									value={field.value}
									onValueChange={field.onChange}
									disabled={isLoadingWebhooks}>
									<SelectTrigger className="h-11 border border-neutral-600">
										<SelectValue
											placeholder={
												isLoadingWebhooks
													? 'Loading webhooks...'
													: webhooks.length === 0
														? 'No webhooks found'
														: 'Select webhook...'
											}
										/>
									</SelectTrigger>
									<SelectContent>
										<ScrollArea className="h-96">
											{webhooks.map((webhook) => (
												<SelectItem key={webhook.id} value={webhook.id}>
													<div className="flex max-w-64 flex-col truncate text-start">
														<span className="truncate font-medium">
															{webhook.name}
														</span>
														<span className="truncate text-xs text-gray-500">
															{webhook.webhookUrl}
														</span>
													</div>
												</SelectItem>
											))}
										</ScrollArea>
									</SelectContent>
								</Select>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			{/* Mail Template Selector */}
			<div className="space-y-3">
				<Typography>
					Mail Template <span className="text-red-500">*</span>
				</Typography>
				<FormField
					control={form.control}
					name="mail"
					render={({ field }) => (
						<FormItem>
							<FormControl>
								<Select
									value={field.value}
									onValueChange={field.onChange}
									disabled={!brandValue || isLoadingTemplates}>
									<SelectTrigger className="h-11 border border-neutral-600">
										<SelectValue
											placeholder={
												!brandValue
													? 'Select brand first...'
													: isLoadingTemplates
														? 'Loading templates...'
														: mailTemplates.length === 0
															? 'No templates found for this brand'
															: 'Select mail template...'
											}
										/>
									</SelectTrigger>
									<SelectContent>
										<ScrollArea className="h-96">
											{mailTemplates.map((template) => (
												<SelectItem
													key={template._id}
													value={template._id || ''}>
													<div className="flex items-center gap-2">
														<Mail className="size-4" />
														<div className="flex max-w-64 flex-col truncate text-start">
															<span className="font-medium">
																{template.senderName}
															</span>
															<span className="text-xs text-gray-500">
																{template.sender}
															</span>
														</div>
													</div>
												</SelectItem>
											))}
										</ScrollArea>
									</SelectContent>
								</Select>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>
			<div className={'space-y-3'}>
				<Typography>Thời gian bắt đầu</Typography>
				<div className={'flex items-center gap-2'}>
					<Select
						defaultValue={'0'}
						value={form.watch('startDate')?.getHours().toString()}
						onValueChange={(value) => {
							const startDate = form.watch('startDate');
							startDate?.setHours(Number(value));
							form.setValue('startDate', startDate);
						}}>
						<SelectTrigger className={'h-11 border border-neutral-600'}>
							<SelectValue /> Giờ
						</SelectTrigger>
						<SelectContent>
							{/*	24h */}
							<ScrollArea className={'h-96'}>
								<div>
									{Array.from({ length: 24 }).map((_, index) => (
										<SelectItem
											key={index}
											value={index.toString()}
											className={'text-black'}>
											{index}
										</SelectItem>
									))}
								</div>
							</ScrollArea>
						</SelectContent>
					</Select>
					<Select
						defaultValue={'0'}
						onValueChange={(value) => {
							const startDate = form.watch('startDate');
							startDate?.setMinutes(Number(value));
							form.setValue('startDate', startDate);
						}}
						value={form.watch('startDate')?.getMinutes().toString()}>
						<SelectTrigger className={'h-11 border border-neutral-600'}>
							<SelectValue /> Phút
						</SelectTrigger>
						<SelectContent>
							{/*	60m */}
							<ScrollArea className={'h-96'}>
								<div>
									{Array.from({ length: 60 }).map((_, index) => (
										<SelectItem
											key={index}
											value={index.toString()}
											className={'text-black'}>
											{index}
										</SelectItem>
									))}
								</div>
							</ScrollArea>
						</SelectContent>
					</Select>
				</div>
				<Popover>
					<PopoverTrigger asChild>
						<Button
							type="button"
							variant={'outline'}
							className={'h-11 w-full justify-start'}>
							<Calendar1 />
							{format(form.watch('startDate') || new Date(), 'dd/MM/yyyy')}
						</Button>
					</PopoverTrigger>
					<PopoverContent
						className={'w-fit border-none bg-transparent p-0 backdrop-blur-xl'}>
						<FormField
							render={({ field }) => (
								<Calendar
									mode="single"
									selected={field.value}
									onSelect={(value) => {
										if (value) {
											const startDate = form.watch('startDate');
											startDate?.setFullYear(value.getFullYear());
											startDate?.setMonth(value.getMonth());
											startDate?.setDate(value.getDate());
											form.setValue('startDate', startDate);
										} else {
											form.setValue('startDate', new Date());
										}
									}}
									className="rounded-md border"
									disabled={(date) => date < subDays(new Date(), 1)}
								/>
							)}
							name={'startDate'}
							control={form.control}
						/>
					</PopoverContent>
				</Popover>
			</div>
			<FormField
				control={form.control}
				name="file"
				render={({ field: { value, onChange, ...fieldProps } }) => (
					<FormItem className={'flex-1'}>
						<FormLabel>
							Upload File{' '}
							{!isEditMode && <span className="text-red-500">*</span>}
							{isEditMode && (
								<span className="text-gray-500">
									(Optional - leave empty to keep current file)
								</span>
							)}
						</FormLabel>
						<FormControl>
							<div className="relative flex items-center rounded-lg border border-[#424242] transition-colors hover:border-gray-300">
								<Button
									type="button"
									variant={'outline'}
									className="flex h-11 items-center border-transparent bg-transparent text-base font-medium">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="inline w-6"
										viewBox="0 0 32 32">
										<path d="M23.75 11.044a7.99 7.99 0 0 0-15.5-.009A8 8 0 0 0 9 27h3a1 1 0 0 0 0-2H9a6 6 0 0 1-.035-12 1.038 1.038 0 0 0 1.1-.854 5.991 5.991 0 0 1 11.862 0A1.08 1.08 0 0 0 23 13a6 6 0 0 1 0 12h-3a1 1 0 0 0 0 2h3a8 8 0 0 0 .75-15.956z" />
										<path d="M20.293 19.707a1 1 0 0 0 1.414-1.414l-5-5a1 1 0 0 0-1.414 0l-5 5a1 1 0 0 0 1.414 1.414L15 16.414V29a1 1 0 0 0 2 0V16.414z" />
									</svg>
									{!value ? 'Upload file' : value.name}
								</Button>
								<input
									type="file"
									multiple={false}
									className="absolute inset-0 cursor-pointer opacity-0"
									accept=".csv"
									onChange={(e) => {
										onChange(e.target.files?.[0] || null);
									}}
									{...fieldProps}
								/>
							</div>
						</FormControl>
						<FormMessage />
					</FormItem>
				)}
			/>
			<Button type={'submit'} disabled={isLoading} className={'w-full'}>
				<p className={'leading-4'}>
					{isLoading ? 'Processing...' : submitButtonText}
				</p>
			</Button>
		</div>
	);
};
