'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import {
	Sheet,
	SheetContent,
	SheetTitle,
	SheetTrigger,
} from '@/components/ui/sheet';
import { FormProvider, useForm } from 'react-hook-form';
import { createCamp } from '@/services/camp/create-camp';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { TCampForm } from '@/features/camps/schemas/camps-schema';
import { CampForm } from '@/features/camps/create/CampForm';

export const CreateCampDialog = () => {
	const methods = useForm<TCampForm>({
		defaultValues: {
			brand: '',
			webhook: '',
			mail: '',
			startDate: new Date(),
		},
	});
	const queryClient = useQueryClient();

	const mutation = useMutation({
		mutationKey: ['camps'],
		mutationFn: createCamp,
		onSuccess: async () => {
			await queryClient.invalidateQueries({ queryKey: ['camps'] });
			methods.reset();
		},
	});

	const onSubmit = async (data: TCampForm) => {
		console.log(data);
		mutation.mutate(data);
	};

	return (
		<Sheet>
			<SheetTrigger asChild>
				<Button
					type="submit"
					variant={'default'}
					className={'size-11 rounded-full'}>
					<Plus />
				</Button>
			</SheetTrigger>
			<SheetContent className={'p-4'}>
				<SheetTitle>Create Camp</SheetTitle>
				<FormProvider {...methods}>
					<form onSubmit={methods.handleSubmit(onSubmit)}>
						<CampForm />
					</form>
				</FormProvider>
			</SheetContent>
		</Sheet>
	);
};
