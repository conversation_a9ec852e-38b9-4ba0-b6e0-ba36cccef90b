'use client';
import { useParams } from 'next/navigation';
import { getDetailCamp } from '@/services/camp/get-detail-camp';
import { useQuery } from '@tanstack/react-query';
import Typography from '@/components/shared/Typography';
import { upperCaseFirstLetter } from '@/utils/string';
import { format } from 'date-fns';
import { getCampFile } from '@/services/camp/get-file';
	import { parse } from 'csv-parse';
import { CampTableItem } from '@/types/camp';
import { Suspense, useEffect, useState } from 'react';
import { CampTable } from '@/features/camps/detail/CampTable';
import { ThemeSwitch } from '@/components/theme-switch';
import { ProfileDropdown } from '@/components/profile-dropdown';
import { Header } from '@/components/layout/header';
import { Main } from '@/components/layout/main';
import { Card, CardContent } from '@/components/ui/card';
import { BsPcDisplay } from 'react-icons/bs';
import { IoIosRocket } from 'react-icons/io';
import { TbClockBolt } from 'react-icons/tb';
import { FaRegUser } from 'react-icons/fa';
import { BiTargetLock } from 'react-icons/bi';
import { GiDuration } from 'react-icons/gi';

export default function CampDetailContainer() {
	const { id } = useParams<{ id: string }>();
	const [table, setTableData] = useState<CampTableItem[]>([]);

	const { data, isLoading, error } = useQuery({
		queryKey: ['camp', id],
		queryFn: () => getDetailCamp(id),
		enabled: !!id,
		retry: 2,
	});

	const fetchData = async () => {
		const csvText = await getCampFile(id);

		if (!csvText) return;
		const list: CampTableItem[] = [];
		parse(csvText, {
			columns: true,
			skip_empty_lines: true,
			trim: true,
		})
			.on('data', (row: CampTableItem) => {
				list.push(row);
			})
			.on('end', () => {
				setTableData(list);
			})
			.on('error', (error) => {
				console.error('❌ Error reading CSV:', error);
			});
	};

	useEffect(() => {
		if (id) {
			fetchData().then();
		}
	}, [id]);

	if (isLoading) {
		return <div>Loading...</div>;
	}
	if (error || !data) {
		return <div>Camp not found!</div>;
	}

	return (
		<div>
			<Header fixed>
				<div className="ml-auto flex items-center space-x-4">
					<ThemeSwitch />
					<ProfileDropdown />
				</div>
			</Header>
			<Main>
				<div className={'flex gap-4'}>
					<Card>
						<CardContent>
							<div className={'flex items-center gap-2'}>
								<BsPcDisplay />
								<Typography tag={'h1'} variant={'16px_f700_l24'}>
									{upperCaseFirstLetter(data.brand)}
								</Typography>
							</div>
							<div className={'flex items-center gap-2'}>
								<IoIosRocket />
								<Typography>{data.webhook.name}</Typography>
							</div>
							<div className={'flex items-center gap-2'}>
								<TbClockBolt />
								<Typography tag={'h1'}>
									{format(new Date(data.startDate), 'HH:mm dd/MM/yyyy')}
								</Typography>
							</div>
							<div className={'flex items-center gap-2'}>
								<FaRegUser />
								<Typography>{data.createdBy}</Typography>
							</div>
							<div className={'flex items-center gap-2'}>
								<BiTargetLock />
								<Typography>{table.length}</Typography>
							</div>
							<div className={'flex items-center gap-2'}>
								<GiDuration />
								<Typography>{data.createdAt}s</Typography>
							</div>
						</CardContent>
					</Card>
				</div>
				<div className={'mt-5 space-y-4'}>
					<Suspense>
						<CampTable data={table} />
					</Suspense>
				</div>
			</Main>
		</div>
	);
}
