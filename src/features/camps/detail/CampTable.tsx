'use client';
import { useEffect } from 'react';
import { CampTableItem } from '@/types/camp';
import { usePagination } from '@/hooks/use-pagination';
import { ListPagination } from '@/components/shared/Panigation/ListPagination';
import { useSearchParams } from 'next/navigation';
import { isNumber } from 'lodash';

export const CampTable = ({ data }: { data: CampTableItem[] }) => {
	const page = Number(useSearchParams().get('page')) || 1;

	useEffect(() => {
		if (isNumber(page) && page > 0) {
			goToPage(page);
		}
	}, [page]);

	const { goToPage, currentPage, paginatedItems } = usePagination({
		totalItems: data.length,
		itemsPerPage: 10,
		initialPage: page,
	});

	return (
		<>
			<div className="w-full overflow-hidden overflow-x-auto rounded-xl border border-gray-700">
				<table className="w-full table-auto border-collapse text-left text-sm">
					<thead className="bg-gray-800 text-white dark:bg-gray-200 dark:text-black">
						<tr>
							<th className="w-64 px-4 py-2">Email</th>
							<th className="w-24 px-4 py-2">Country</th>
							<th className="w-64 px-4 py-2">Fullname</th>
							<th className="w-64 px-4 py-2">Job</th>
							<th className="max-w-[600px] px-4 py-2">TinyURL</th>
						</tr>
					</thead>
					<tbody className="divide-y divide-gray-700">
						{data
							.slice(paginatedItems[0], paginatedItems[1])
							.map((row: CampTableItem) => (
								<tr key={row.EMAIL}>
									<td className="w-64 truncate px-4 py-2">{row.EMAIL}</td>
									<td className="w-24 truncate px-4 py-2">{row.COUNTRY}</td>
									<td className="w-64 truncate px-4 py-2">{row.FULLNAME}</td>
									<td className="w-64 truncate px-4 py-2">{row.JOB}</td>
									<td className="max-w-[600px] truncate px-4 py-2">
										{row.TINYURL}
									</td>
								</tr>
							))}
					</tbody>
				</table>
			</div>
			<ListPagination
				totalPages={Math.ceil(data.length / 10)}
				currentPage={currentPage}
			/>
		</>
	);
};
