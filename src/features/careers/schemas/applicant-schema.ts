import z from 'zod';

// Applicant form schema for creating/editing
export const applicantFormSchema = z.object({
	email: z.string().email('<PERSON>ail không hợp lệ').min(1, '<PERSON>ail là bắt buộc'),
	fullName: z.string().min(1, '<PERSON><PERSON> tên là bắt buộc').max(255, 'Họ tên quá dài'),
	phone: z.string().optional(),
	address: z.string().optional(),
	secondaryPhone: z.string().optional(),
	jobId: z.string().min(1, 'Job là bắt buộc'),
	pageId: z.string().optional(),
	pageCode: z.string().optional(),
	tag: z.string().optional(),
});

// Complete applicant schema with all database fields (for create/update operations)
export const applicantSchema = z.object({
	_id: z.string(),
	email: z.string(),
	fullName: z.string(),
	phone: z.string().optional(),
	address: z.string().optional(),
	resume: z.string().optional(),
	secondaryPhone: z.string().optional(),
	jobId: z.string(),
	pageId: z.string().optional(),
	pageCode: z.string().optional(),
	clientInfo: z.object({
		ip: z.string().optional(),
		userAgent: z.string().optional(),
		country: z.string().optional(),
	}).optional(),
	createdBy: z.string().optional(),
	startedDate: z.string().optional(),
	endedDate: z.string().optional(),
	tag: z.string().optional(),
	createdAt: z.string(),
	updatedAt: z.string().optional(),
	updatedBy: z.union([z.string(), z.number()]).optional(),
});

// Applicant detail schema with job and page info (API response format)
export const applicantDetailSchema = z.object({
	_id: z.string(),
	email: z.string(),
	fullName: z.string(),
	phone: z.string().optional(),
	address: z.string().optional(),
	resume: z.string().optional(),
	secondaryPhone: z.string().optional(),
	clientInfo: z.object({
		ip: z.string().optional(),
		userAgent: z.string().optional(),
		country: z.string().optional(),
	}).optional(),
	job: z.object({
		_id: z.string(),
		title: z.string(),
		description: z.string().optional(),
		content: z.string(),
		address: z.string(),
		status: z.enum(['activated', 'deactivated']),
		createdAt: z.string(),
		createdBy: z.string(),
		updatedAt: z.string().optional(),
		updatedBy: z.union([z.string(), z.number()]).optional(),
	}).optional(),
	page: z.object({
		_id: z.string(),
		title: z.string(),
		description: z.string().optional(),
		status: z.enum(['activated', 'deactivated']),
		code: z.string(),
		createdAt: z.string(),
		createdBy: z.string(),
	}).optional(),
	createdBy: z.string().optional(),
	startedDate: z.string().optional(),
	endedDate: z.string().optional(),
	tag: z.string().optional(),
	createdAt: z.string(),
	updatedAt: z.string().optional(),
	updatedBy: z.union([z.string(), z.number()]).optional(),
});

// Schema for updating applicant (all fields optional except ID)
export const applicantUpdateSchema = applicantFormSchema.partial().extend({
	_id: z.string().min(1, 'ID is required'),
});

export type TApplicantForm = z.infer<typeof applicantFormSchema>;
export type TApplicant = z.infer<typeof applicantSchema>;
export type TApplicantDetail = z.infer<typeof applicantDetailSchema>;
export type TApplicantUpdate = z.infer<typeof applicantUpdateSchema>;
