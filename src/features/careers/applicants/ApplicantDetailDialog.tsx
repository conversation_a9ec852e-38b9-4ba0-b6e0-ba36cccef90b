'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { format } from 'date-fns';
import {
	Mail,
	Phone,
	Calendar,
	User,
	FileText,
	Tag,
	Building2,
	Layout,
	Download,
	Eye,
	MapPin,
	Globe,
	Monitor,
	Smartphone,
	Laptop,
	Tablet
} from 'lucide-react';
import { toast } from 'sonner';

import { downloadApplicantFile } from '@/services/careers/applicant';
import { TApplicantDetail } from '@/features/careers/schemas/applicant-schema';

interface ApplicantDetailDialogProps {
	applicant: TApplicantDetail;
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

export const ApplicantDetailDialog = ({ applicant, open, onOpenChange }: ApplicantDetailDialogProps) => {
	const [pdfUrl, setPdfUrl] = useState<string | null>(null);

	// Use job and page data from applicant object (already included in response)
	const jobDetail = applicant.job;
	const pageDetail = applicant.page;

	// Parse User Agent for better display
	const parseUserAgent = (userAgent: string) => {
		const ua = userAgent.toLowerCase();

		// Detect Browser
		let browser = 'Unknown';
		let browserVersion = '';
		if (ua.includes('chrome') && !ua.includes('edg')) {
			browser = 'Chrome';
			const match = userAgent.match(/Chrome\/([0-9.]+)/);
			browserVersion = match ? match[1] : '';
		} else if (ua.includes('firefox')) {
			browser = 'Firefox';
			const match = userAgent.match(/Firefox\/([0-9.]+)/);
			browserVersion = match ? match[1] : '';
		} else if (ua.includes('safari') && !ua.includes('chrome')) {
			browser = 'Safari';
			const match = userAgent.match(/Version\/([0-9.]+)/);
			browserVersion = match ? match[1] : '';
		} else if (ua.includes('edg')) {
			browser = 'Edge';
			const match = userAgent.match(/Edg\/([0-9.]+)/);
			browserVersion = match ? match[1] : '';
		}

		// Detect Operating System
		let os = 'Unknown';
		let osVersion = '';
		if (ua.includes('windows')) {
			os = 'Windows';
			if (ua.includes('windows nt 10.0')) osVersion = '10/11';
			else if (ua.includes('windows nt 6.3')) osVersion = '8.1';
			else if (ua.includes('windows nt 6.2')) osVersion = '8';
			else if (ua.includes('windows nt 6.1')) osVersion = '7';
		} else if (ua.includes('mac os x')) {
			os = 'macOS';
			const match = userAgent.match(/Mac OS X ([0-9_]+)/);
			if (match) {
				osVersion = match[1].replace(/_/g, '.');
			}
		} else if (ua.includes('linux')) {
			os = 'Linux';
		} else if (ua.includes('android')) {
			os = 'Android';
			const match = userAgent.match(/Android ([0-9.]+)/);
			osVersion = match ? match[1] : '';
		} else if (ua.includes('iphone') || ua.includes('ipad')) {
			os = ua.includes('ipad') ? 'iPadOS' : 'iOS';
			const match = userAgent.match(/OS ([0-9_]+)/);
			if (match) {
				osVersion = match[1].replace(/_/g, '.');
			}
		}

		// Detect Device Type
		let deviceType = 'Desktop';
		let deviceIcon = Laptop;
		if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
			deviceType = 'Mobile';
			deviceIcon = Smartphone;
		} else if (ua.includes('tablet') || ua.includes('ipad')) {
			deviceType = 'Tablet';
			deviceIcon = Tablet;
		}

		return {
			browser,
			browserVersion,
			os,
			osVersion,
			deviceType,
			deviceIcon
		};
	};

	const handleDownloadCV = async () => {
		try {
			const blob = await downloadApplicantFile(applicant._id);
			const url = window.URL.createObjectURL(blob);
			const a = document.createElement('a');
			a.style.display = 'none';
			a.href = url;
			a.download = `${applicant.fullName}_CV.pdf`;
			document.body.appendChild(a);
			a.click();
			window.URL.revokeObjectURL(url);
			toast.success('CV đã được tải xuống!');
		} catch (error) {
			toast.error('Lỗi khi tải CV');
		}
	};

	const handleViewCV = async () => {
		try {
			const blob = await downloadApplicantFile(applicant._id);
			const url = window.URL.createObjectURL(blob);
			setPdfUrl(url);
		} catch (error) {
			toast.error('Lỗi khi xem CV');
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<User className="h-5 w-5" />
						{applicant.fullName}
					</DialogTitle>
					<DialogDescription>
						Chi tiết ứng viên
					</DialogDescription>
				</DialogHeader>

				<Tabs defaultValue="info" className="space-y-4">
					<TabsList className="grid w-full grid-cols-3">
						<TabsTrigger value="info">Thông tin</TabsTrigger>
						<TabsTrigger value="job">Job & Page</TabsTrigger>
						<TabsTrigger value="cv">CV</TabsTrigger>
					</TabsList>

					{/* Applicant Info Tab */}
					<TabsContent value="info" className="space-y-4">
						<Card>
							<CardHeader>
								<CardTitle className="text-lg">Thông tin cá nhân</CardTitle>
							</CardHeader>
							<CardContent className="space-y-4">
								<div className="grid grid-cols-2 gap-4">
									<div className="flex items-center gap-2">
										<User className="h-4 w-4 text-muted-foreground" />
										<div>
											<p className="text-sm font-medium">Họ tên</p>
											<p className="text-sm text-muted-foreground">{applicant.fullName}</p>
										</div>
									</div>

									<div className="flex items-center gap-2">
										<Mail className="h-4 w-4 text-muted-foreground" />
										<div>
											<p className="text-sm font-medium">Email</p>
											<p className="text-sm text-muted-foreground">{applicant.email}</p>
										</div>
									</div>

									{applicant.phone && (
										<div className="flex items-center gap-2">
											<Phone className="h-4 w-4 text-muted-foreground" />
											<div>
												<p className="text-sm font-medium">Số điện thoại</p>
												<p className="text-sm text-muted-foreground">{applicant.phone}</p>
											</div>
										</div>
									)}

									{applicant.address && (
										<div className="flex items-center gap-2">
											<MapPin className="h-4 w-4 text-muted-foreground" />
											<div>
												<p className="text-sm font-medium">Địa chỉ</p>
												<p className="text-sm text-muted-foreground">{applicant.address}</p>
											</div>
										</div>
									)}

									<div className="flex items-center gap-2">
										<Calendar className="h-4 w-4 text-muted-foreground" />
										<div>
											<p className="text-sm font-medium">Ngày apply</p>
											<p className="text-sm text-muted-foreground">
												{format(new Date(applicant.createdAt), 'dd/MM/yyyy HH:mm')}
											</p>
										</div>
									</div>

									{applicant.tag && (
										<div className="flex items-center gap-2">
											<Tag className="h-4 w-4 text-muted-foreground" />
											<div>
												<p className="text-sm font-medium">Tag</p>
												<Badge variant="outline">{applicant.tag}</Badge>
											</div>
										</div>
									)}

									{applicant.startedDate && (
										<div className="flex items-center gap-2">
											<Calendar className="h-4 w-4 text-muted-foreground" />
											<div>
												<p className="text-sm font-medium">Ngày bắt đầu</p>
												<p className="text-sm text-muted-foreground">
													{format(new Date(applicant.startedDate), 'dd/MM/yyyy')}
												</p>
											</div>
										</div>
									)}

									{applicant.endedDate && (
										<div className="flex items-center gap-2">
											<Calendar className="h-4 w-4 text-muted-foreground" />
											<div>
												<p className="text-sm font-medium">Ngày kết thúc</p>
												<p className="text-sm text-muted-foreground">
													{format(new Date(applicant.endedDate), 'dd/MM/yyyy')}
												</p>
											</div>
										</div>
									)}
								</div>
							</CardContent>
						</Card>

						{/* Client Info Card */}
						{applicant.clientInfo && (
							<Card>
								<CardHeader>
									<CardTitle className="text-lg">Thông tin truy cập</CardTitle>
								</CardHeader>
								<CardContent className="space-y-4">
									<div className="grid grid-cols-2 gap-4">
										{applicant.clientInfo.ip && (
											<div className="flex items-center gap-2">
												<Globe className="h-4 w-4 text-muted-foreground" />
												<div>
													<p className="text-sm font-medium">IP Address</p>
													<p className="text-sm text-muted-foreground">{applicant.clientInfo.ip}</p>
												</div>
											</div>
										)}

										{applicant.clientInfo.country && (
											<div className="flex items-center gap-2">
												<MapPin className="h-4 w-4 text-muted-foreground" />
												<div>
													<p className="text-sm font-medium">Quốc gia</p>
													<p className="text-sm text-muted-foreground">{applicant.clientInfo.country}</p>
												</div>
											</div>
										)}
									</div>

									{applicant.clientInfo.userAgent && (() => {
										const userAgentInfo = parseUserAgent(applicant.clientInfo.userAgent);
										const DeviceIcon = userAgentInfo.deviceIcon;

										return (
											<div className="col-span-2">
												<div className="flex items-start gap-2 mb-3">
													<DeviceIcon className="h-4 w-4 text-muted-foreground mt-1" />
													<div className="flex-1">
														<p className="text-sm font-medium">Thông tin thiết bị</p>
													</div>
												</div>

												<div className="grid grid-cols-3 gap-4 ml-6">
													<div className="space-y-1">
														<p className="text-xs font-medium text-muted-foreground">Trình duyệt</p>
														<div className="flex items-center gap-1">
															<Badge variant="outline" className="text-xs">
																{userAgentInfo.browser}
															</Badge>
															{userAgentInfo.browserVersion && (
																<span className="text-xs text-muted-foreground">
																	v{userAgentInfo.browserVersion.split('.')[0]}
																</span>
															)}
														</div>
													</div>

													<div className="space-y-1">
														<p className="text-xs font-medium text-muted-foreground">Hệ điều hành</p>
														<div className="flex items-center gap-1">
															<Badge variant="outline" className="text-xs">
																{userAgentInfo.os}
															</Badge>
															{userAgentInfo.osVersion && (
																<span className="text-xs text-muted-foreground">
																	{userAgentInfo.osVersion.split('.').slice(0, 2).join('.')}
																</span>
															)}
														</div>
													</div>

													<div className="space-y-1">
														<p className="text-xs font-medium text-muted-foreground">Loại thiết bị</p>
														<Badge variant="secondary" className="text-xs">
															{userAgentInfo.deviceType}
														</Badge>
													</div>
												</div>

												<details className="mt-3 ml-6">
													<summary className="text-xs text-muted-foreground cursor-pointer hover:text-foreground">
														Xem User Agent đầy đủ
													</summary>
													<p className="text-xs text-muted-foreground break-all mt-2 p-2 bg-muted rounded">
														{applicant.clientInfo.userAgent}
													</p>
												</details>
											</div>
										);
									})()}
								</CardContent>
							</Card>
						)}
					</TabsContent>

					{/* Job & Page Info Tab */}
					<TabsContent value="job" className="space-y-4">
						{jobDetail && (
							<Card>
								<CardHeader>
									<CardTitle className="text-lg flex items-center gap-2">
										<FileText className="h-4 w-4" />
										Job Information
									</CardTitle>
								</CardHeader>
								<CardContent className="space-y-4">
									<div>
										<p className="text-sm font-medium">Tiêu đề Job</p>
										<p className="text-lg">{jobDetail.title}</p>
									</div>

									{jobDetail.description && (
										<div>
											<p className="text-sm font-medium">Mô tả</p>
											<p className="text-sm text-muted-foreground">{jobDetail.description}</p>
										</div>
									)}

									<div>
										<p className="text-sm font-medium">Địa chỉ</p>
										<p className="text-sm text-muted-foreground">{jobDetail.address}</p>
									</div>

									{jobDetail.tags && jobDetail.tags.length > 0 && (
										<div>
											<p className="text-sm font-medium mb-2">Tags</p>
											<div className="flex flex-wrap gap-2">
												{jobDetail.tags.map((tag) => (
													<Badge key={tag} variant="outline">
														{tag}
													</Badge>
												))}
											</div>
										</div>
									)}
								</CardContent>
							</Card>
						)}

						{pageDetail && (
							<Card>
								<CardHeader>
									<CardTitle className="text-lg flex items-center gap-2">
										<Layout className="h-4 w-4" />
										Page Information
									</CardTitle>
								</CardHeader>
								<CardContent className="space-y-4">
									<div>
										<p className="text-sm font-medium">Tiêu đề Page</p>
										<p className="text-lg">{pageDetail.title}</p>
									</div>

									{pageDetail.description && (
										<div>
											<p className="text-sm font-medium">Mô tả</p>
											<p className="text-sm text-muted-foreground">{pageDetail.description}</p>
										</div>
									)}

									{pageDetail.domain && (
										<div className="flex items-center gap-2">
											<Building2 className="h-4 w-4 text-muted-foreground" />
											<div>
												<p className="text-sm font-medium">Domain</p>
												<p className="text-sm text-muted-foreground">
													{pageDetail.domain.name}
												</p>
											</div>
										</div>
									)}
								</CardContent>
							</Card>
						)}
					</TabsContent>

					{/* CV Tab */}
					<TabsContent value="cv" className="space-y-4">
						<Card>
							<CardHeader>
								<CardTitle className="text-lg">CV của ứng viên</CardTitle>
							</CardHeader>
							<CardContent className="space-y-4">
								<div className="flex gap-2">
									<Button onClick={handleViewCV} variant="outline">
										<Eye className="mr-2 h-4 w-4" />
										Xem CV
									</Button>
									<Button onClick={handleDownloadCV}>
										<Download className="mr-2 h-4 w-4" />
										Tải xuống CV
									</Button>
								</div>

								{pdfUrl && (
									<div className="border rounded-lg overflow-hidden">
										<iframe
											src={pdfUrl}
											width="100%"
											height="600px"
											title="CV Preview"
											className="border-0"
										/>
									</div>
								)}
							</CardContent>
						</Card>
					</TabsContent>
				</Tabs>

				<DialogFooter>
					<Button onClick={() => onOpenChange(false)}>
						Đóng
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};
