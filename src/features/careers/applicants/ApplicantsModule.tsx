'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Search, MoreHorizontal, Eye, Download, Trash2, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';

import { toast } from 'sonner';
import { format } from 'date-fns';

import { getApplicants, deleteApplicant, downloadApplicantFile } from '@/services/careers/applicant';
import { getJobs } from '@/services/careers/job';
import { getPages } from '@/services/careers/page';
import { TApplicant } from '@/features/careers/schemas/applicant-schema';
import { ApplicantDetailDialog } from './ApplicantDetailDialog';
import { ApplicantFiltersDialog } from './ApplicantFiltersDialog';

export const ApplicantsModule = () => {
	const [search, setSearch] = useState('');
	const [selectedJobId, setSelectedJobId] = useState<string>('all');
	const [selectedPageId, setSelectedPageId] = useState<string>('all');
	const [selectedApplicant, setSelectedApplicant] = useState<TApplicant | null>(null);
	const [showDetailDialog, setShowDetailDialog] = useState(false);
	const [showFiltersDialog, setShowFiltersDialog] = useState(false);
	const [additionalFilters, setAdditionalFilters] = useState<any>({});

	// Fetch applicants
	const { data: applicantsResponse, isLoading } = useQuery({
		queryKey: ['applicants', search, selectedJobId, selectedPageId, additionalFilters],
		queryFn: () => getApplicants({
			fullName: search || undefined,
			email: search || undefined,
			jobId: selectedJobId === 'all' ? undefined : selectedJobId,
			pageId: selectedPageId === 'all' ? undefined : selectedPageId,
			...additionalFilters,
			limit: 50
		}),
	});

	// Fetch jobs for filter
	const { data: jobsResponse } = useQuery({
		queryKey: ['jobs'],
		queryFn: () => getJobs({ limit: 100 }),
	});

	// Fetch pages for filter
	const { data: pagesResponse } = useQuery({
		queryKey: ['pages'],
		queryFn: () => getPages({ limit: 100 }),
	});

	const applicants = applicantsResponse?.data || [];
	const jobs = jobsResponse?.data || [];
	const pages = pagesResponse?.data || [];

	const handleViewDetail = (applicant: TApplicant) => {
		setSelectedApplicant(applicant);
		setShowDetailDialog(true);
	};

	const handleDownload = async (applicant: TApplicant) => {
		try {
			const blob = await downloadApplicantFile(applicant._id);
			const url = window.URL.createObjectURL(blob);
			const a = document.createElement('a');
			a.style.display = 'none';
			a.href = url;
			a.download = `${applicant.fullName}_CV.pdf`;
			document.body.appendChild(a);
			a.click();
			window.URL.revokeObjectURL(url);
			toast.success('File đã được tải xuống!');
		} catch (error) {
			toast.error('Lỗi khi tải file');
		}
	};

	const handleDelete = async (applicant: TApplicant) => {
		if (confirm(`Bạn có chắc chắn muốn xóa ứng viên "${applicant.fullName}"?`)) {
			try {
				await deleteApplicant(applicant._id);
				toast.success('Ứng viên đã được xóa!');
				// Refresh data will happen automatically due to query invalidation
			} catch (error) {
				toast.error('Lỗi khi xóa ứng viên');
			}
		}
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h2 className="text-2xl font-bold tracking-tight">Applicants</h2>
					<p className="text-muted-foreground">
						Quản lý các ứng viên apply job
					</p>
				</div>
				<Button onClick={() => setShowFiltersDialog(true)} variant="outline">
					<Filter className="mr-2 h-4 w-4" />
					Bộ lọc nâng cao
				</Button>
			</div>

			{/* Filters */}
			<div className="flex items-center space-x-2">
				<div className="relative flex-1 max-w-sm">
					<Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
					<Input
						placeholder="Tìm kiếm theo tên hoặc email..."
						value={search}
						onChange={(e) => setSearch(e.target.value)}
						className="pl-8"
					/>
				</div>

				<Select value={selectedPageId} onValueChange={setSelectedPageId}>
					<SelectTrigger className="w-[200px]">
						<SelectValue placeholder="Chọn page" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">Tất cả pages</SelectItem>
						{pages.map((page) => (
							<SelectItem key={page._id} value={page._id}>
								{page.title}
							</SelectItem>
						))}
					</SelectContent>
				</Select>

				<Select value={selectedJobId} onValueChange={setSelectedJobId}>
					<SelectTrigger className="w-[200px]">
						<SelectValue placeholder="Chọn job" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">Tất cả jobs</SelectItem>
						{jobs.map((job) => (
							<SelectItem key={job._id} value={job._id}>
								{job.title}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
			</div>

			{/* Applicants Table */}
			<Card>
				<CardHeader>
					<CardTitle>Danh sách Applicants ({applicants.length})</CardTitle>
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="flex items-center justify-center py-8">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
						</div>
					) : (
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Họ tên</TableHead>
									<TableHead>Email</TableHead>
									<TableHead>Số điện thoại</TableHead>
									<TableHead>Job</TableHead>
									<TableHead>Page</TableHead>
									<TableHead>Ngày apply</TableHead>
									<TableHead className="text-right">Thao tác</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{applicants.map((applicant) => (
									<TableRow key={applicant._id}>
										<TableCell className="font-medium">
											{applicant.fullName}
										</TableCell>
										<TableCell>
											{applicant.email}
										</TableCell>
										<TableCell>
											{applicant.phone || '-'}
										</TableCell>
										<TableCell>
											{jobs.find(j => j._id === applicant.jobId)?.title || applicant.jobId}
										</TableCell>
										<TableCell>
											{pages.find(p => p._id === applicant.pageId)?.title || applicant.pageCode}
										</TableCell>
										<TableCell>
											{format(new Date(applicant.createdAt), 'dd/MM/yyyy HH:mm')}
										</TableCell>
										<TableCell className="text-right">
											<DropdownMenu>
												<DropdownMenuTrigger asChild>
													<Button variant="ghost" className="h-8 w-8 p-0">
														<MoreHorizontal className="h-4 w-4" />
													</Button>
												</DropdownMenuTrigger>
												<DropdownMenuContent align="end">
													<DropdownMenuItem onClick={() => handleViewDetail(applicant)}>
														<Eye className="mr-2 h-4 w-4" />
														Xem chi tiết
													</DropdownMenuItem>
													<DropdownMenuItem onClick={() => handleDownload(applicant)}>
														<Download className="mr-2 h-4 w-4" />
														Tải CV
													</DropdownMenuItem>
													<DropdownMenuItem
														onClick={() => handleDelete(applicant)}
														className="text-red-600"
													>
														<Trash2 className="mr-2 h-4 w-4" />
														Xóa
													</DropdownMenuItem>
												</DropdownMenuContent>
											</DropdownMenu>
										</TableCell>
									</TableRow>
								))}
								{applicants.length === 0 && (
									<TableRow>
										<TableCell colSpan={7} className="text-center py-8">
											Không có ứng viên nào
										</TableCell>
									</TableRow>
								)}
							</TableBody>
						</Table>
					)}
				</CardContent>
			</Card>

			{/* Dialogs */}
			{selectedApplicant && (
				<ApplicantDetailDialog
					applicant={selectedApplicant}
					open={showDetailDialog}
					onOpenChange={setShowDetailDialog}
				/>
			)}

			<ApplicantFiltersDialog
				open={showFiltersDialog}
				onOpenChange={setShowFiltersDialog}
				onFiltersChange={setAdditionalFilters}
				currentFilters={additionalFilters}
			/>
		</div>
	);
};
