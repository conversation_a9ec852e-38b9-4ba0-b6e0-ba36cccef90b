'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Plus, Search, MoreHorizontal, Edit, ToggleLeft, ToggleRight, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { format } from 'date-fns';

import { getJobs, updateJobStatus } from '@/services/careers/job';
import { getPages } from '@/services/careers/page';
import { TJob } from '@/features/careers/schemas/job-schema';
import { CreateJobDialog } from './CreateJobDialog';
import { EditJobDialog } from './EditJobDialog';
import { JobDetailDialog } from './JobDetailDialog';

export const JobsModule = () => {
	const [search, setSearch] = useState('');
	const [selectedPageId, setSelectedPageId] = useState<string>('all');
	const [selectedStatus, setSelectedStatus] = useState<string>('all');
	const [selectedJob, setSelectedJob] = useState<TJob | null>(null);
	const [showCreateDialog, setShowCreateDialog] = useState(false);
	const [showEditDialog, setShowEditDialog] = useState(false);
	const [showDetailDialog, setShowDetailDialog] = useState(false);

	// Fetch jobs
	const { data: jobsResponse, isLoading } = useQuery({
		queryKey: ['jobs', search, selectedPageId, selectedStatus],
		queryFn: () => getJobs({
			search,
			pageId: selectedPageId === 'all' ? undefined : selectedPageId,
			status: selectedStatus === 'all' ? undefined : selectedStatus as 'activated' | 'deactivated',
			limit: 50
		}),
	});

	// Fetch pages for filter
	const { data: pagesResponse } = useQuery({
		queryKey: ['pages'],
		queryFn: () => getPages({ limit: 100 }),
	});

	const jobs = jobsResponse?.data || [];
	const pages = pagesResponse?.data || [];

	const handleEdit = (job: TJob) => {
		setSelectedJob(job);
		setShowEditDialog(true);
	};

	const handleViewDetail = (job: TJob) => {
		setSelectedJob(job);
		setShowDetailDialog(true);
	};

	const handleToggleStatus = async (job: TJob) => {
		try {
			await updateJobStatus(job._id);
			toast.success('Trạng thái job đã được cập nhật!');
			// Refresh data
		} catch (error) {
			toast.error('Lỗi khi cập nhật trạng thái job');
		}
	};

	// Helper function to truncate HTML content
	const truncateHtml = (html: string, maxLength: number = 100) => {
		const div = document.createElement('div');
		div.innerHTML = html;
		const text = div.textContent || div.innerText || '';
		return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h2 className="text-2xl font-bold tracking-tight">Jobs</h2>
					<p className="text-muted-foreground">
						Quản lý các job posting trong từng page
					</p>
				</div>
				<Button onClick={() => setShowCreateDialog(true)}>
					<Plus className="mr-2 h-4 w-4" />
					Thêm Job
				</Button>
			</div>

			{/* Filters */}
			<div className="flex items-center space-x-2">
				<div className="relative flex-1 max-w-sm">
					<Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
					<Input
						placeholder="Tìm kiếm job..."
						value={search}
						onChange={(e) => setSearch(e.target.value)}
						className="pl-8"
					/>
				</div>

				<Select value={selectedPageId} onValueChange={setSelectedPageId}>
					<SelectTrigger className="w-[200px]">
						<SelectValue placeholder="Chọn page" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">Tất cả pages</SelectItem>
						{pages.map((page) => (
							<SelectItem key={page._id} value={page._id}>
								{page.title}
							</SelectItem>
						))}
					</SelectContent>
				</Select>

				<Select value={selectedStatus} onValueChange={setSelectedStatus}>
					<SelectTrigger className="w-[150px]">
						<SelectValue placeholder="Trạng thái" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">Tất cả</SelectItem>
						<SelectItem value="activated">Kích hoạt</SelectItem>
						<SelectItem value="deactivated">Vô hiệu hóa</SelectItem>
					</SelectContent>
				</Select>
			</div>

			{/* Jobs Table */}
			<Card>
				<CardHeader>
					<CardTitle>Danh sách Jobs ({jobs.length})</CardTitle>
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="flex items-center justify-center py-8">
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
						</div>
					) : (
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Tiêu đề</TableHead>
									<TableHead>Page</TableHead>
									<TableHead>Địa chỉ</TableHead>
									<TableHead>Nội dung</TableHead>
									<TableHead>Trạng thái</TableHead>
									<TableHead>Ngày tạo</TableHead>
									<TableHead className="text-right">Thao tác</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{jobs.map((job) => (
									<TableRow key={job._id}>
										<TableCell className="font-medium">
											{job.title}
										</TableCell>
										<TableCell>
											{pages.find(p => p._id === job.pageId)?.title || job.pageCode}
										</TableCell>
										<TableCell>
											{job.address}
										</TableCell>
										<TableCell className="max-w-[200px]">
											<div className="truncate">
												{truncateHtml(job.content)}
											</div>
										</TableCell>
										<TableCell>
											<Badge
												variant={job.status === 'activated' ? 'default' : 'secondary'}
											>
												{job.status === 'activated' ? 'Kích hoạt' : 'Vô hiệu hóa'}
											</Badge>
										</TableCell>
										<TableCell>
											{format(new Date(job.createdAt), 'dd/MM/yyyy HH:mm')}
										</TableCell>
										<TableCell className="text-right">
											<DropdownMenu>
												<DropdownMenuTrigger asChild>
													<Button variant="ghost" className="h-8 w-8 p-0">
														<MoreHorizontal className="h-4 w-4" />
													</Button>
												</DropdownMenuTrigger>
												<DropdownMenuContent align="end">
													<DropdownMenuItem onClick={() => handleViewDetail(job)}>
														<Eye className="mr-2 h-4 w-4" />
														Xem chi tiết
													</DropdownMenuItem>
													<DropdownMenuItem onClick={() => handleEdit(job)}>
														<Edit className="mr-2 h-4 w-4" />
														Chỉnh sửa
													</DropdownMenuItem>
													<DropdownMenuItem onClick={() => handleToggleStatus(job)}>
														{job.status === 'activated' ? (
															<>
																<ToggleLeft className="mr-2 h-4 w-4" />
																Vô hiệu hóa
															</>
														) : (
															<>
																<ToggleRight className="mr-2 h-4 w-4" />
																Kích hoạt
															</>
														)}
													</DropdownMenuItem>
												</DropdownMenuContent>
											</DropdownMenu>
										</TableCell>
									</TableRow>
								))}
								{jobs.length === 0 && (
									<TableRow>
										<TableCell colSpan={7} className="text-center py-8">
											Không có job nào
										</TableCell>
									</TableRow>
								)}
							</TableBody>
						</Table>
					)}
				</CardContent>
			</Card>

			{/* Dialogs */}
			<CreateJobDialog
				open={showCreateDialog}
				onOpenChange={setShowCreateDialog}
			/>

			{selectedJob && (
				<>
					<EditJobDialog
						job={selectedJob}
						open={showEditDialog}
						onOpenChange={setShowEditDialog}
					/>
					<JobDetailDialog
						job={selectedJob}
						open={showDetailDialog}
						onOpenChange={setShowDetailDialog}
					/>
				</>
			)}
		</div>
	);
};
