'use client';

import { useQuery } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { format } from 'date-fns';
import {
	Building2,
	Calendar,
	FileText,
	Layout,
	MapPin,
	Tag,
	User,
} from 'lucide-react';

import { getPageDetail } from '@/services/careers/page';
import { TJob } from '@/features/careers/schemas/job-schema';

interface JobDetailDialogProps {
	job: TJob;
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

export const JobDetailDialog = ({
	job,
	open,
	onOpenChange,
}: JobDetailDialogProps) => {
	// Fetch page details if pageId exists
	const { data: pageDetail } = useQuery({
		queryKey: ['page', job.pageId],
		queryFn: () => (job.pageId ? getPageDetail(job.pageId) : null),
		enabled: !!job.pageId,
	});

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[900px]">
				<DialogHeader>
					<DialogTitle className="flex items-center gap-2">
						<FileText className="h-5 w-5" />
						{job.title}
					</DialogTitle>
					<DialogDescription>Chi tiết job posting</DialogDescription>
				</DialogHeader>

				<div className="space-y-6">
					{/* Basic Info */}
					<Card>
						<CardHeader>
							<CardTitle className="text-lg">Thông tin cơ bản</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="grid grid-cols-2 gap-4">
								<div className="flex items-center gap-2">
									<MapPin className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Địa chỉ</p>
										<p className="text-muted-foreground text-sm">
											{job.address}
										</p>
									</div>
								</div>

								<div className="flex items-center gap-2">
									<Layout className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Page</p>
										<p className="text-muted-foreground text-sm">
											{pageDetail?.title || job.pageCode}
										</p>
									</div>
								</div>

								<div className="flex items-center gap-2">
									<Calendar className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Ngày tạo</p>
										<p className="text-muted-foreground text-sm">
											{format(new Date(job.createdAt), 'dd/MM/yyyy HH:mm')}
										</p>
									</div>
								</div>

								<div className="flex items-center gap-2">
									<User className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Người tạo</p>
										<p className="text-muted-foreground text-sm">
											{job.createdBy}
										</p>
									</div>
								</div>
							</div>

							{pageDetail?.domain && (
								<div className="flex items-center gap-2">
									<Building2 className="text-muted-foreground h-4 w-4" />
									<div>
										<p className="text-sm font-medium">Domain</p>
										<p className="text-muted-foreground text-sm">
											{pageDetail.domain.name}
										</p>
									</div>
								</div>
							)}

							<div className="flex items-center gap-2">
								<div className="flex h-4 w-4 items-center justify-center">
									<div
										className={`h-2 w-2 rounded-full ${
											job.status === 'activated'
												? 'bg-green-500'
												: 'bg-gray-400'
										}`}
									/>
								</div>
								<div>
									<p className="text-sm font-medium">Trạng thái</p>
									<Badge
										variant={
											job.status === 'activated' ? 'default' : 'secondary'
										}>
										{job.status === 'activated' ? 'Kích hoạt' : 'Vô hiệu hóa'}
									</Badge>
								</div>
							</div>
						</CardContent>
					</Card>

					{/* Description */}
					{job.description && (
						<Card>
							<CardHeader>
								<CardTitle className="text-lg">Mô tả ngắn</CardTitle>
							</CardHeader>
							<CardContent>
								<p className="text-muted-foreground text-sm">
									{job.description}
								</p>
							</CardContent>
						</Card>
					)}

					{/* Tags */}
					{job.tags && job.tags.length > 0 && (
						<Card>
							<CardHeader>
								<CardTitle className="flex items-center gap-2 text-lg">
									<Tag className="h-4 w-4" />
									Tags
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="flex flex-wrap gap-2">
									{job.tags.map((tag) => (
										<Badge key={tag} variant="outline">
											{tag}
										</Badge>
									))}
								</div>
							</CardContent>
						</Card>
					)}

					{/* Content */}
					<Card>
						<CardHeader>
							<CardTitle className="text-lg">Nội dung Job</CardTitle>
						</CardHeader>
						<CardContent>
							<div
								className="prose prose-sm sm:prose-base max-w-none
										   prose-headings:text-gray-900 prose-p:text-gray-700
										   prose-strong:text-gray-900 prose-em:text-gray-600
										   prose-ul:text-gray-700 prose-ol:text-gray-700
										   prose-blockquote:text-gray-600 prose-blockquote:border-l-blue-500
										   prose-li:text-gray-700"
								dangerouslySetInnerHTML={{ __html: job.content }}
							/>
						</CardContent>
					</Card>

					{/* URL Preview */}
					{pageDetail?.domain && (
						<Card>
							<CardHeader>
								<CardTitle className="text-lg">URL Preview</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="space-y-2">
									<p className="text-sm font-medium">Job URL:</p>
									<code className="bg-muted block rounded p-2 text-sm">
										{pageDetail.domain.name}/{job.pageCode}/jobs/{job._id}
									</code>
									<p className="text-sm font-medium">Apply URL:</p>
									<code className="bg-muted block rounded p-2 text-sm">
										{pageDetail.domain.name}/{job.pageCode}/jobs/{job._id}/apply
									</code>
								</div>
							</CardContent>
						</Card>
					)}
				</div>

				<DialogFooter>
					<Button onClick={() => onOpenChange(false)}>Đóng</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
};
