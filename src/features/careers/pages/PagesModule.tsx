'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
	Edit,
	MoreHorizontal,
	Plus,
	Search,
	ToggleLeft,
	ToggleRight,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { format } from 'date-fns';

import { getPages, updatePageStatus } from '@/services/careers/page';
import { getDomains } from '@/services/careers/domain';
import { TPage } from '@/features/careers/schemas/page-schema';
import { CreatePageDialog } from './CreatePageDialog';
import { EditPageDialog } from './EditPageDialog';

export const PagesModule = () => {
	const [search, setSearch] = useState('');
	const [selectedDomainId, setSelectedDomainId] = useState<string>('all');
	const [selectedStatus, setSelectedStatus] = useState<string>('all');
	const [selectedPage, setSelectedPage] = useState<TPage | null>(null);
	const [showCreateDialog, setShowCreateDialog] = useState(false);
	const [showEditDialog, setShowEditDialog] = useState(false);

	// Fetch pages
	const { data: pagesResponse, isLoading } = useQuery({
		queryKey: ['pages', search, selectedDomainId, selectedStatus],
		queryFn: () =>
			getPages({
				search,
				domainId: selectedDomainId === 'all' ? undefined : selectedDomainId,
				status: selectedStatus === 'all' ? undefined : (selectedStatus as 'activated' | 'deactivated'),
				limit: 50,
			}),
	});

	// Fetch domains for filter
	const { data: domainsResponse } = useQuery({
		queryKey: ['domains'],
		queryFn: () => getDomains({ limit: 100 }),
	});

	const pages = pagesResponse?.data || [];
	const domains = domainsResponse?.data || [];

	const handleEdit = (page: TPage) => {
		setSelectedPage(page);
		setShowEditDialog(true);
	};

	const handleToggleStatus = async (page: TPage) => {
		try {
			await updatePageStatus(page._id);
			toast.success('Trạng thái page đã được cập nhật!');
			// Refresh data
		} catch (error) {
			toast.error('Lỗi khi cập nhật trạng thái page');
		}
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h2 className="text-2xl font-bold tracking-tight">Pages</h2>
					<p className="text-muted-foreground">
						Quản lý các page trong từng domain
					</p>
				</div>
				<Button onClick={() => setShowCreateDialog(true)}>
					<Plus className="mr-2 h-4 w-4" />
					Thêm Page
				</Button>
			</div>

			{/* Filters */}
			<div className="flex items-center space-x-2">
				<div className="relative max-w-sm flex-1">
					<Search className="text-muted-foreground absolute top-2.5 left-2 h-4 w-4" />
					<Input
						placeholder="Tìm kiếm page..."
						value={search}
						onChange={(e) => setSearch(e.target.value)}
						className="pl-8"
					/>
				</div>

				<Select value={selectedDomainId} onValueChange={setSelectedDomainId}>
					<SelectTrigger className="w-[200px]">
						<SelectValue placeholder="Chọn domain" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">Tất cả domains</SelectItem>
						{domains.map((domain) => (
							<SelectItem key={domain._id} value={domain._id}>
								{domain.name}
							</SelectItem>
						))}
					</SelectContent>
				</Select>

				<Select value={selectedStatus} onValueChange={setSelectedStatus}>
					<SelectTrigger className="w-[150px]">
						<SelectValue placeholder="Trạng thái" />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="all">Tất cả</SelectItem>
						<SelectItem value="activated">Kích hoạt</SelectItem>
						<SelectItem value="deactivated">Vô hiệu hóa</SelectItem>
					</SelectContent>
				</Select>
			</div>

			{/* Pages Table */}
			<Card>
				<CardHeader>
					<CardTitle>Danh sách Pages ({pages.length})</CardTitle>
				</CardHeader>
				<CardContent>
					{isLoading ? (
						<div className="flex items-center justify-center py-8">
							<div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"></div>
						</div>
					) : (
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Tiêu đề</TableHead>
									<TableHead>Domain</TableHead>
									<TableHead>Mô tả</TableHead>
									<TableHead>Trạng thái</TableHead>
									<TableHead>Ngày tạo</TableHead>
									<TableHead className="text-right">Thao tác</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{pages.map((page) => (
									<TableRow key={page._id}>
										<TableCell className="font-medium">{page.title}</TableCell>
										<TableCell>
											{domains.find((d) => d._id === page.domainId)?.name ||
												page.domainId}
										</TableCell>
										<TableCell>{page.description || '-'}</TableCell>
										<TableCell>
											<Badge
												variant={
													page.status === 'activated' ? 'default' : 'secondary'
												}>
												{page.status === 'activated'
													? 'Kích hoạt'
													: 'Vô hiệu hóa'}
											</Badge>
										</TableCell>
										<TableCell>
											{format(new Date(page.createdAt), 'dd/MM/yyyy HH:mm')}
										</TableCell>
										<TableCell className="text-right">
											<DropdownMenu>
												<DropdownMenuTrigger asChild>
													<Button variant="ghost" className="h-8 w-8 p-0">
														<MoreHorizontal className="h-4 w-4" />
													</Button>
												</DropdownMenuTrigger>
												<DropdownMenuContent align="end">
													<DropdownMenuItem onClick={() => handleEdit(page)}>
														<Edit className="mr-2 h-4 w-4" />
														Chỉnh sửa
													</DropdownMenuItem>
													<DropdownMenuItem
														onClick={() => handleToggleStatus(page)}>
														{page.status === 'activated' ? (
															<>
																<ToggleLeft className="mr-2 h-4 w-4" />
																Vô hiệu hóa
															</>
														) : (
															<>
																<ToggleRight className="mr-2 h-4 w-4" />
																Kích hoạt
															</>
														)}
													</DropdownMenuItem>
												</DropdownMenuContent>
											</DropdownMenu>
										</TableCell>
									</TableRow>
								))}
								{pages.length === 0 && (
									<TableRow>
										<TableCell colSpan={6} className="py-8 text-center">
											Không có page nào
										</TableCell>
									</TableRow>
								)}
							</TableBody>
						</Table>
					)}
				</CardContent>
			</Card>

			{/* Dialogs */}
			<CreatePageDialog
				open={showCreateDialog}
				onOpenChange={setShowCreateDialog}
			/>

			{selectedPage && (
				<EditPageDialog
					page={selectedPage}
					open={showEditDialog}
					onOpenChange={setShowEditDialog}
				/>
			)}
		</div>
	);
};
