'use client';
import { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Header } from '@/components/layout/header';
import { ThemeSwitch } from '@/components/theme-switch';
import { ProfileDropdown } from '@/components/profile-dropdown';
import { Main } from '@/components/layout/main';
import { Button } from '@/components/ui/button';
import ShortLinkForm from '@/features/short-links/form/shortlink-form';
import { ShortLinkLiveView } from '@/features/short-links/live-view';
import {
	fileShortLinkFormSchema,
	TShortlinkForm,
} from '@/features/short-links/data/shema';
import { AllowDomain } from '@/features/short-links/data/domain';
import { DomainSelector } from './components/domain-selector';
import { LogsPanel } from './components/logs-panel';
import { useShortlinkProcessor } from './hooks/use-shortlink-processor';

export default function ShortLink() {
	const methods = useForm<TShortlinkForm>({
		resolver: zodResolver(fileShortLinkFormSchema),
		defaultValues: {
			domain: '',
			prefix: '',
			expirationLink: '',
			quantity: '1',
		},
	});

	const [selectedDomain, setSelectedDomain] = useState(AllowDomain[0]);
	const { results, isRunning, stepLogger, processForm } =
		useShortlinkProcessor();

	const onSubmit = async (form: TShortlinkForm) => {
		await processForm(form, selectedDomain);
	};

	return (
		<>
			<Header fixed>
				<div className="ml-auto flex items-center space-x-4">
					<ThemeSwitch />
					<ProfileDropdown />
				</div>
			</Header>
			<Main>
				<FormProvider {...methods}>
					<div className="flex gap-16">
						<div className="w-64">
							<h2 className="text-2xl font-bold tracking-tight">Shortlink</h2>
							<p className="text-muted-foreground mb-4">
								Generate shortlink for your file.
							</p>

							<DomainSelector onDomainChange={setSelectedDomain} />

							<form
								id="shortlink-form"
								onSubmit={methods.handleSubmit(onSubmit)}>
								<ShortLinkForm />
								<Button
									type="submit"
									className="mt-5 w-full"
									disabled={isRunning}>
									<p className="leading-4">
										{isRunning ? 'Đang xử lý...' : 'Tạo liên kết'}
									</p>
								</Button>
							</form>
						</div>
						<div className="flex-1">
							<LogsPanel logs={stepLogger} />
							<ShortLinkLiveView results={results} isRunning={isRunning} />
						</div>
					</div>
				</FormProvider>
			</Main>
		</>
	);
}
