'use client';
import {Head<PERSON>} from '@/components/layout/header';
import {Main} from '@/components/layout/main';
import {ProfileDropdown} from '@/components/profile-dropdown';
import {ThemeSwitch} from '@/components/theme-switch';
import {UsersDialogs} from './components/dialog/users-dialogs';
import {UsersPrimaryButtons} from './components/users-primary-buttons';
import {UsersTable} from './components/table/users-table';
import UsersProvider from './context/users-context';
import {columns} from "@/features/users/components/table/users-columns";

export default function Users() {

  return (
    <UsersProvider>
      <Header fixed>
        <div className="ml-auto flex items-center space-x-4">
          <ThemeSwitch/>
          <ProfileDropdown/>
        </div>
      </Header>

      <Main>
        <div className="mb-2 flex flex-wrap items-center justify-between space-y-2">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">User List</h2>
            <p className="text-muted-foreground">
              Manage your users and their roles here.
            </p>
          </div>
          <UsersPrimaryButtons/>
        </div>
        <div className="-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12">
          <UsersTable columns={columns}/>
        </div>
      </Main>
      <UsersDialogs/>
    </UsersProvider>
  );
}
