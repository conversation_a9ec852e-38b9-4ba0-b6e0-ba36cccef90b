'use client';

import { useForm } from 'react-hook-form';
import { showSubmittedData } from '@/utils/show-submitted-data';
import { Button } from '@/components/ui/button';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { SelectDropdown } from '@/components/select-dropdown';
import { userTypes } from '../../data/data';
import { User } from '../../data/schema';
import { updateUser } from '@/services/users/update-user';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { UpdateUserParams } from '@/types/users';

interface Props {
	currentRow?: User;
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

export function UsersActionDialog({ currentRow, open, onOpenChange }: Props) {
	const isEdit = !!currentRow;
	const form = useForm({
		defaultValues: isEdit
			? {
					...currentRow,
					password: '',
					confirmPassword: '',
					isEdit,
				}
			: {
					firstName: '',
					lastName: '',
					username: '',
					email: '',
					role: '',
					phoneNumber: '',
					password: '',
					confirmPassword: '',
					isEdit,
				},
	});

	const queryClient = useQueryClient();

	const mutation = useMutation({
		mutationKey: ['users'],
		mutationFn: async (data: { user: UpdateUserParams; id: string }) => {
			if (isEdit) {
				return await updateUser(data.user, data.id);
			}
		},
		onSuccess: async () => {
			await queryClient.invalidateQueries({ queryKey: ['users'] });
			onOpenChange(false);
		},
	});

	const onSubmit = (values: any) => {
		if (isEdit) {
			mutation.mutate({
				user: {
					role: values.role,
					credit: values.credit,
				},
				id: currentRow._id,
			});
		} else {
			showSubmittedData(values);
			onOpenChange(false);
		}
	};

	return (
		<Dialog
			open={open}
			onOpenChange={(state) => {
				form.reset();
				onOpenChange(state);
			}}>
			<DialogContent className="sm:max-w-lg">
				<DialogHeader className="text-left">
					<DialogTitle>{isEdit ? 'Edit User' : 'Add New User'}</DialogTitle>
					<DialogDescription>
						{isEdit ? 'Update the user here. ' : 'Create new user here. '}
						Click save when you&apos;re done.
					</DialogDescription>
				</DialogHeader>
				<div className="-mr-4 w-full overflow-y-auto py-1 pr-4">
					<Form {...form}>
						<form
							id="user-form"
							onSubmit={form.handleSubmit(onSubmit)}
							className="space-y-4 p-0.5">
							<FormField
								control={form.control}
								name="firstName"
								render={({ field }) => (
									<FormItem className="grid grid-cols-6 items-center space-y-0 gap-x-4 gap-y-1">
										<FormLabel className="col-span-2 text-right">
											First Name
										</FormLabel>
										<FormControl>
											<Input
												placeholder="John"
												containerClassName={'col-span-4'}
												autoComplete="off"
												disabled={true}
												{...field}
											/>
										</FormControl>
										<FormMessage className="col-span-4 col-start-3" />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="lastName"
								render={({ field }) => (
									<FormItem className="grid grid-cols-6 items-center space-y-0 gap-x-4 gap-y-1">
										<FormLabel className="col-span-2 text-right">
											Last Name
										</FormLabel>
										<FormControl>
											<Input
												placeholder="Doe"
												containerClassName={'col-span-4'}
												autoComplete="off"
												disabled={true}
												{...field}
											/>
										</FormControl>
										<FormMessage className="col-span-4 col-start-3" />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="username"
								render={({ field }) => (
									<FormItem className="grid grid-cols-6 items-center space-y-0 gap-x-4 gap-y-1">
										<FormLabel className="col-span-2 text-right">
											Username
										</FormLabel>
										<FormControl>
											<Input
												placeholder="john_doe"
												disabled={true}
												containerClassName={'col-span-4'}
												{...field}
											/>
										</FormControl>
										<FormMessage className="col-span-4 col-start-3" />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="role"
								render={({ field }) => (
									<FormItem className="grid grid-cols-6 items-center space-y-0 gap-x-4 gap-y-1">
										<FormLabel className="col-span-2 text-right">
											Role
										</FormLabel>
										<SelectDropdown
											defaultValue={field.value}
											onValueChange={field.onChange}
											placeholder="Select a role"
											className={'col-span-4'}
											items={userTypes
												.filter(
													(el) =>
														el.value !== 'super_admin' && el.value != 'manager'
												)
												.map(({ label, value }) => ({
													label,
													value,
												}))}
										/>
										<FormMessage className="col-span-4 col-start-3" />
									</FormItem>
								)}
							/>
						</form>
					</Form>
				</div>
				<DialogFooter>
					<Button type="submit" form="user-form">
						Save changes
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
