import {z} from 'zod'

const userRoleSchema = z.union([
  z.literal('super_admin'),
  z.literal('admin'),
  z.literal('manager'),
  z.literal('user'),
])

export type TUserRole = z.infer<typeof userRoleSchema>

const userSchema = z.object({
  createdAt: z.coerce.date(),
  firstName: z.string(),
  languageCode: z.string(),
  lastName: z.string(),
  telegramId: z.string(),
  updatedAt: z.coerce.date(),
  username: z.string(),
  role: userRoleSchema,
  _id: z.string(),
})
export type User = z.infer<typeof userSchema>

export const userListSchema = z.array(userSchema)
