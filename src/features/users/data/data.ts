import {IconShield, IconUserFilled, IconUsersGroup, IconUserShield,} from '@tabler/icons-react'
import {TUserRole} from "@/features/users/data/schema";

type UserType = {
  label: string
  value: TUserRole
  icon: React.ComponentType<{ className?: string, size?: number }>
}

export const userTypes: UserType[] = [
  {
    label: 'Super Admin',
    value: 'super_admin',
    icon: IconShield,
  },
  {
    label: 'Admin',
    value: 'admin',
    icon: IconUserShield,
  },
  {
    label: 'Manager',
    value: 'manager',
    icon: IconUsersGroup,
  },
  {
    label: 'User',
    value: 'user',
    icon: IconUserFilled,
  },
] as const
