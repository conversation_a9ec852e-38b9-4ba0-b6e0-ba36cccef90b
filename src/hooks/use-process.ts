import { useState, useCallback, useEffect, useRef } from 'react';

/**
 * Custom hook để xử lý tiến trình với mô phỏng tiến độ
 * @returns {Object} Trạng thái hook và các phương thức
 */
export const useProcess = () => {
	const [progress, setProgress] = useState<number>(0);
	const [isRunning, setIsRunning] = useState<boolean>(false);
	const intervalRef = useRef<NodeJS.Timeout | null>(null);
	const timeoutIdsRef = useRef<NodeJS.Timeout[]>([]);

	// Xóa tất cả timeout và interval
	const clearAllTimers = useCallback(() => {
		if (intervalRef.current) {
			clearInterval(intervalRef.current);
			intervalRef.current = null;
		}

		timeoutIdsRef.current.forEach(id => clearTimeout(id));
		timeoutIdsRef.current = [];
	}, []);

	// Reset tiến trình về 0
	const reset = useCallback(() => {
		clearAllTimers();
		setProgress(0);
		setIsRunning(false);
	}, [clearAllTimers]);

	// Bắt đầu mô phỏng tiến trình
	const start = useCallback(() => {
		// Reset trước để đảm bảo bắt đầu từ 0
		reset();
		setIsRunning(true);

		// Tạo dãy các mốc tiến độ ngẫu nhiên
		const milestones = [
			{ target: 15, delay: 300 },           // Tiến độ ban đầu nhanh
			{ target: 35, delay: 600 },           // Sau đó chậm lại một chút
			{ target: 50, delay: 800 },           // Điểm giữa
			{ target: 65, delay: 1000 },          // Tiếp tục chậm hơn
			{ target: 80, delay: 1500 },          // Chậm hơn nữa
			{ target: 90, delay: 2500 },          // Gần hoàn thành nhưng rất chậm
			{ target: 95, delay: 3500 },          // Giai đoạn cuối cùng
			{ target: 99, delay: 5000 }           // Tối đa trước khi hoàn thành thủ công
		];

		// Lên lịch cho từng mốc
		let cumulativeDelay = 0;
		milestones.forEach(({ target, delay }) => {
			cumulativeDelay += delay;
			const timeoutId = setTimeout(() => {
				setProgress(target);
			}, cumulativeDelay);

			timeoutIdsRef.current.push(timeoutId);
		});

		// Thêm các gia tăng nhỏ ngẫu nhiên giữa các mốc để tạo hiệu ứng tự nhiên hơn
		intervalRef.current = setInterval(() => {
			setProgress(prev => {
				if (prev >= 99) return 99;

				// Gia tăng nhỏ ngẫu nhiên (0.1 đến 0.5)
				const increment = Math.random() * 0.4 + 0.1;

				// Mốc tiếp theo cần tôn trọng
				const nextMilestone = milestones.find(m => m.target > prev);
				if (nextMilestone) {
					// Không vượt quá mốc dự kiến tiếp theo
					return Math.min(prev + increment, nextMilestone.target - 0.1);
				}

				// Giới hạn ở 99%
				return Math.min(prev + increment, 99);
			});
		}, 200);

	}, [reset, clearAllTimers]);

	// Hoàn thành tiến trình và đặt tiến độ lên 100%
	const finish = useCallback(() => {
		clearAllTimers();
		setProgress(100);
		setIsRunning(false);

		return new Promise<void>(resolve => {
			// Độ trễ nhỏ trước khi giải quyết để cho phép UI hiển thị 100%
			const finishTimeout = setTimeout(() => {
				resolve();
			}, 300);

			timeoutIdsRef.current.push(finishTimeout);
		});
	}, [clearAllTimers]);

	// Hủy tiến trình
	const cancel = useCallback(() => {
		clearAllTimers();
		setIsRunning(false);
	}, [clearAllTimers]);

	// Dọn dẹp khi component unmount
	useEffect(() => {
		return () => {
			clearAllTimers();
		};
	}, [clearAllTimers]);

	return {
		progress,
		isRunning,
		start,
		finish,
		reset,
		cancel
	};
};
