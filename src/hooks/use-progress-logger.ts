import { useState } from 'react';
import { LogEntry } from '@/features/short-links/components/logs-panel';

export const useProgressLogger = () => {
	const [stepLogger, setStepLogger] = useState<LogEntry[]>([]);

	const addAndUpdateLog = (id: string, message: string) => {
		setStepLogger((prev) => {
			const existingIndex = prev.findIndex((item) => item.id === id);
			const timestamp = new Date();
			if (existingIndex !== -1) {
				const newLogs = [...prev];
				newLogs[existingIndex] = { id, message, timestamp }; // Cập nhật log cho id
				return newLogs;
			} else {
				const newLogs = [...prev];

				if (newLogs.length >= 10) {
					newLogs.shift();
				}

				newLogs.push({ id, message, timestamp }); // Thêm log mới
				return newLogs;
			}
		});
	};

	const clearLog = () => {
		setStepLogger([]);
	};

	const removeLog = (id: string) => {
		setStepLogger((prev) => prev.filter((item) => item.id !== id));
	};

	const updateLog = (id: string, message: string) => {
		setStepLogger((prev) => {
			const existingIndex = prev.findIndex((item) => item.id === id);
			if (existingIndex !== -1) {
				const timestamp = new Date();
				const newLogs = [...prev];
				newLogs[existingIndex] = { id, message, timestamp };
				return newLogs;
			}
			return prev;
		});
	};

	const getLog = (id: string) => {
		return stepLogger.find((item) => item.id === id);
	};

	return {
		stepLogger,
		addAndUpdateLog,
		clearLog,
		removeLog,
		updateLog,
		getLog,
	};
};
