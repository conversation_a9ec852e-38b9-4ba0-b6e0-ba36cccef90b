import React from 'react';
import { LoadingOverlay } from '@/components/shared/Zoomies';
import { getCookie } from '@/utils/cookies';
import { cn } from '@/lib/utils';
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/layout/app-sidebar';
import SkipToMain from '@/components/skip-to-main';
import { headers } from 'next/headers';
import { extractTitleFromPathName } from '@/utils/url-parser';

export async function generateMetadata() {
	const headersList = await headers();
	const pathname = headersList.get('x-pathname') || '';

	const siteTitle = extractTitleFromPathName(pathname);

	return {
		title: `${siteTitle} | x54`,
	};
}

export default function Layout({ children }: ChildrenProp) {
	const defaultOpen = getCookie('sidebar_state') !== 'false';
	return (
		<LoadingOverlay duration={2000} backgroundOpacity={1}>
			<SidebarProvider defaultOpen={defaultOpen}>
				<SkipToMain />
				<AppSidebar />
				<div
					id="content"
					className={cn(
						'ml-auto w-full max-w-full',
						'peer-data-[state=collapsed]:w-[calc(100%-var(--sidebar-width-icon)-1rem)]',
						'peer-data-[state=expanded]:w-[calc(100%-var(--sidebar-width))]',
						'sm:transition-[width] sm:duration-200 sm:ease-linear',
						'flex flex-col',
						'group-data-[scroll-locked=1]/body:h-full',
						'has-[main.fixed-main]:group-data-[scroll-locked=1]/body:h-svh'
					)}>
					{children}
				</div>
			</SidebarProvider>
		</LoadingOverlay>
	);
}
