'use client';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { ChevronDownIcon } from '@radix-ui/react-icons';
import { zodResolver } from '@hookform/resolvers/zod';
import { fonts } from '@/config/fonts';
import { cn } from '@/lib/utils';
import { showSubmittedData } from '@/utils/show-submitted-data';
import { useFont } from '@/context/FontContext';
import { useTheme } from '@/context/ThemeContext';
import { Button, buttonVariants } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useEffect } from 'react';

const appearanceFormSchema = z.object({
	mode: z.enum(['light', 'dark', 'system'], {
		required_error: 'Please select a mode.',
	}),
	theme: z.enum(
		['default', 'red', 'rose', 'orange', 'green', 'blue', 'yellow', 'violet'],
		{
			required_error: 'Please select a theme.',
		}
	),
	font: z.enum(fonts, {
		invalid_type_error: 'Select a font',
		required_error: 'Please select a font.',
	}),
});

type AppearanceFormValues = z.infer<typeof appearanceFormSchema>;

export function AppearanceForm() {
	const { font, setFont } = useFont();
	const { mode, theme, setMode, setTheme } = useTheme();

	// This can come from your database or API.
	const defaultValues: Partial<AppearanceFormValues> = {
		mode: mode,
		theme: theme,
		font,
	};

	const form = useForm<AppearanceFormValues>({
		resolver: zodResolver(appearanceFormSchema),
		defaultValues,
	});

	function onSubmit(data: AppearanceFormValues) {
		if (data.font != font) setFont(data.font);
		if (data.mode != mode) setMode(data.mode);
		if (data.theme != theme) setTheme(data.theme);
		showSubmittedData(data);
	}

	useEffect(() => {
		if (mode != form.getValues('mode')) {
			form.setValue('mode', mode);
		}
		if (theme != form.getValues('theme')) {
			form.setValue('theme', theme);
		}
	}, [mode, theme]);

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
				<FormField
					control={form.control}
					name="font"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Font</FormLabel>
							<div className="relative w-max">
								<FormControl>
									<select
										className={cn(
											buttonVariants({ variant: 'outline' }),
											'w-[200px] appearance-none font-normal capitalize'
										)}
										{...field}>
										{fonts.map((font) => (
											<option key={font} value={font}>
												{font}
											</option>
										))}
									</select>
								</FormControl>
								<ChevronDownIcon className="absolute top-2.5 right-3 h-4 w-4 opacity-50" />
							</div>
							<FormDescription className="font-manrope">
								Set the font you want to use in the dashboard.
							</FormDescription>
							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					control={form.control}
					name="mode"
					render={({ field }) => (
						<FormItem className="space-y-1">
							<FormLabel>Mode</FormLabel>
							<FormDescription>
								Select the light or dark mode for the dashboard.
							</FormDescription>
							<FormMessage />
							<RadioGroup
								onValueChange={field.onChange}
								value={field.value}
								className="grid grid-cols-3 pt-2">
								<FormItem>
									<FormLabel className="[&:has([data-state=checked])>div]:border-primary flex-col">
										<FormControl>
											<RadioGroupItem value="light" className="sr-only" />
										</FormControl>
										<div className="border-muted hover:border-accent items-center rounded-md border-2 p-1">
											<div className="space-y-2 rounded-sm bg-[#ecedef] p-2">
												<div className="space-y-2 rounded-md bg-white p-2 shadow-xs">
													<div className="h-2 w-[80px] rounded-lg bg-[#ecedef]" />
													<div className="h-2 w-[100px] rounded-lg bg-[#ecedef]" />
												</div>
												<div className="flex items-center space-x-2 rounded-md bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-[#ecedef]" />
													<div className="h-2 w-[100px] rounded-lg bg-[#ecedef]" />
												</div>
												<div className="flex items-center space-x-2 rounded-md bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-[#ecedef]" />
													<div className="h-2 w-[100px] rounded-lg bg-[#ecedef]" />
												</div>
											</div>
										</div>
										<span className="block w-full p-2 text-center font-normal">
											Light
										</span>
									</FormLabel>
								</FormItem>
								<FormItem>
									<FormLabel className="[&:has([data-state=checked])>div]:border-primary flex-col">
										<FormControl>
											<RadioGroupItem value="dark" className="sr-only" />
										</FormControl>
										<div className="border-muted bg-popover hover:bg-accent hover:text-accent-foreground items-center rounded-md border-2 p-1">
											<div className="space-y-2 rounded-sm bg-slate-950 p-2">
												<div className="space-y-2 rounded-md bg-slate-800 p-2 shadow-xs">
													<div className="h-2 w-[80px] rounded-lg bg-slate-400" />
													<div className="h-2 w-[100px] rounded-lg bg-slate-400" />
												</div>
												<div className="flex items-center space-x-2 rounded-md bg-slate-800 p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-slate-400" />
													<div className="h-2 w-[100px] rounded-lg bg-slate-400" />
												</div>
												<div className="flex items-center space-x-2 rounded-md bg-slate-800 p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-slate-400" />
													<div className="h-2 w-[100px] rounded-lg bg-slate-400" />
												</div>
											</div>
										</div>
										<span className="block w-full p-2 text-center font-normal">
											Dark
										</span>
									</FormLabel>
								</FormItem>
								<FormItem>
									<FormLabel className="[&:has([data-state=checked])>div]:border-primary flex-col">
										<FormControl>
											<RadioGroupItem value="system" className="sr-only" />
										</FormControl>
										<div className="border-muted hover:border-accent relative items-center rounded-md border-2 p-1">
											<div
												className="absolute inset-0 space-y-2 rounded-sm bg-slate-950 p-2"
												style={{
													clipPath:
														'polygon(50% 0, 100% 0, 100% 100%, 50% 100%)',
												}}>
												<div className="space-y-2 rounded-md bg-slate-800 p-2 shadow-xs">
													<div className="h-2 w-[80px] rounded-lg bg-slate-400" />
													<div className="h-2 w-[100px] rounded-lg bg-slate-400" />
												</div>
												<div className="flex items-center space-x-2 rounded-md bg-slate-800 p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-slate-400" />
													<div className="h-2 w-[100px] rounded-lg bg-slate-400" />
												</div>
												<div className="flex items-center space-x-2 rounded-md bg-slate-800 p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-slate-400" />
													<div className="h-2 w-[100px] rounded-lg bg-slate-400" />
												</div>
											</div>
											<div className="space-y-2 rounded-sm bg-[#ecedef] p-2">
												<div className="space-y-2 rounded-md bg-white p-2 shadow-xs">
													<div className="h-2 w-[80px] rounded-lg bg-[#ecedef]" />
													<div className="h-2 w-[100px] rounded-lg bg-[#ecedef]" />
												</div>
												<div className="flex items-center space-x-2 rounded-md bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-[#ecedef]" />
													<div className="h-2 w-[100px] rounded-lg bg-[#ecedef]" />
												</div>
												<div className="flex items-center space-x-2 rounded-md bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-[#ecedef]" />
													<div className="h-2 w-[100px] rounded-lg bg-[#ecedef]" />
												</div>
											</div>
										</div>
										<span className="block w-full p-2 text-center font-normal">
											System
										</span>
									</FormLabel>
								</FormItem>
							</RadioGroup>
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name="theme"
					render={({ field }) => (
						<FormItem className="space-y-1">
							<FormLabel>Theme</FormLabel>
							<FormDescription>
								Select the color theme for the dashboard.
							</FormDescription>
							<FormMessage />
							<RadioGroup
								onValueChange={field.onChange}
								value={field.value}
								className="grid grid-cols-2 gap-6 pt-2">
								<FormItem>
									<FormLabel className="[&:has([data-state=checked])>div]:border-primary">
										<FormControl>
											<RadioGroupItem value="default" className="sr-only" />
										</FormControl>
										<div className="border-muted hover:border-accent items-center rounded-md border-2 p-1">
											<div className="space-y-2 rounded-sm bg-blue-50 p-2">
												<div className="space-y-2 rounded-md border border-blue-100 bg-white p-2 shadow-xs">
													<div className="h-2 w-[80px] rounded-lg bg-blue-200" />
													<div className="h-2 w-[100px] rounded-lg bg-blue-200" />
												</div>
												<div className="flex items-center space-x-2 rounded-md border border-blue-100 bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-blue-500" />
													<div className="h-2 w-[100px] rounded-lg bg-blue-200" />
												</div>
												<div className="flex items-center space-x-2 rounded-md border border-blue-100 bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-blue-300" />
													<div className="h-2 w-[100px] rounded-lg bg-blue-200" />
												</div>
											</div>
										</div>
										<span className="block w-full p-2 text-center font-normal">
											Default
										</span>
									</FormLabel>
								</FormItem>
								<FormItem>
									<FormLabel className="[&:has([data-state=checked])>div]:border-primary">
										<FormControl>
											<RadioGroupItem value="red" className="sr-only" />
										</FormControl>
										<div className="border-muted hover:border-accent items-center rounded-md border-2 p-1">
											<div className="space-y-2 rounded-sm bg-red-50 p-2">
												<div className="space-y-2 rounded-md border border-red-100 bg-white p-2 shadow-xs">
													<div className="h-2 w-[80px] rounded-lg bg-red-200" />
													<div className="h-2 w-[100px] rounded-lg bg-red-200" />
												</div>
												<div className="flex items-center space-x-2 rounded-md border border-red-100 bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-red-500" />
													<div className="h-2 w-[100px] rounded-lg bg-red-200" />
												</div>
												<div className="flex items-center space-x-2 rounded-md border border-red-100 bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-red-300" />
													<div className="h-2 w-[100px] rounded-lg bg-red-200" />
												</div>
											</div>
										</div>
										<span className="block w-full p-2 text-center font-normal">
											Red
										</span>
									</FormLabel>
								</FormItem>
								<FormItem>
									<FormLabel className="[&:has([data-state=checked])>div]:border-primary">
										<FormControl>
											<RadioGroupItem value="rose" className="sr-only" />
										</FormControl>
										<div className="border-muted hover:border-accent items-center rounded-md border-2 p-1">
											<div className="space-y-2 rounded-sm bg-rose-50 p-2">
												<div className="space-y-2 rounded-md border border-rose-100 bg-white p-2 shadow-xs">
													<div className="h-2 w-[80px] rounded-lg bg-rose-200" />
													<div className="h-2 w-[100px] rounded-lg bg-rose-200" />
												</div>
												<div className="flex items-center space-x-2 rounded-md border border-rose-100 bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-rose-500" />
													<div className="h-2 w-[100px] rounded-lg bg-rose-200" />
												</div>
												<div className="flex items-center space-x-2 rounded-md border border-rose-100 bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-rose-300" />
													<div className="h-2 w-[100px] rounded-lg bg-rose-200" />
												</div>
											</div>
										</div>
										<span className="block w-full p-2 text-center font-normal">
											Rose
										</span>
									</FormLabel>
								</FormItem>
								<FormItem>
									<FormLabel className="[&:has([data-state=checked])>div]:border-primary">
										<FormControl>
											<RadioGroupItem value="orange" className="sr-only" />
										</FormControl>
										<div className="border-muted hover:border-accent items-center rounded-md border-2 p-1">
											<div className="space-y-2 rounded-sm bg-orange-50 p-2">
												<div className="space-y-2 rounded-md border border-orange-100 bg-white p-2 shadow-xs">
													<div className="h-2 w-[80px] rounded-lg bg-orange-200" />
													<div className="h-2 w-[100px] rounded-lg bg-orange-200" />
												</div>
												<div className="flex items-center space-x-2 rounded-md border border-orange-100 bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-orange-500" />
													<div className="h-2 w-[100px] rounded-lg bg-orange-200" />
												</div>
												<div className="flex items-center space-x-2 rounded-md border border-orange-100 bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-orange-300" />
													<div className="h-2 w-[100px] rounded-lg bg-orange-200" />
												</div>
											</div>
										</div>
										<span className="block w-full p-2 text-center font-normal">
											Orange
										</span>
									</FormLabel>
								</FormItem>
								<FormItem>
									<FormLabel className="[&:has([data-state=checked])>div]:border-primary">
										<FormControl>
											<RadioGroupItem value="green" className="sr-only" />
										</FormControl>
										<div className="border-muted hover:border-accent items-center rounded-md border-2 p-1">
											<div className="space-y-2 rounded-sm bg-green-50 p-2">
												<div className="space-y-2 rounded-md border border-green-100 bg-white p-2 shadow-xs">
													<div className="h-2 w-[80px] rounded-lg bg-green-200" />
													<div className="h-2 w-[100px] rounded-lg bg-green-200" />
												</div>
												<div className="flex items-center space-x-2 rounded-md border border-green-100 bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-green-500" />
													<div className="h-2 w-[100px] rounded-lg bg-green-200" />
												</div>
												<div className="flex items-center space-x-2 rounded-md border border-green-100 bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-green-300" />
													<div className="h-2 w-[100px] rounded-lg bg-green-200" />
												</div>
											</div>
										</div>
										<span className="block w-full p-2 text-center font-normal">
											Green
										</span>
									</FormLabel>
								</FormItem>
								<FormItem>
									<FormLabel className="[&:has([data-state=checked])>div]:border-primary">
										<FormControl>
											<RadioGroupItem value="blue" className="sr-only" />
										</FormControl>
										<div className="border-muted hover:border-accent items-center rounded-md border-2 p-1">
											<div className="space-y-2 rounded-sm bg-blue-50 p-2">
												<div className="space-y-2 rounded-md border border-blue-100 bg-white p-2 shadow-xs">
													<div className="h-2 w-[80px] rounded-lg bg-blue-200" />
													<div className="h-2 w-[100px] rounded-lg bg-blue-200" />
												</div>
												<div className="flex items-center space-x-2 rounded-md border border-blue-100 bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-blue-500" />
													<div className="h-2 w-[100px] rounded-lg bg-blue-200" />
												</div>
												<div className="flex items-center space-x-2 rounded-md border border-blue-100 bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-blue-300" />
													<div className="h-2 w-[100px] rounded-lg bg-blue-200" />
												</div>
											</div>
										</div>
										<span className="block w-full p-2 text-center font-normal">
											Blue
										</span>
									</FormLabel>
								</FormItem>
								<FormItem>
									<FormLabel className="[&:has([data-state=checked])>div]:border-primary">
										<FormControl>
											<RadioGroupItem value="yellow" className="sr-only" />
										</FormControl>
										<div className="border-muted hover:border-accent items-center rounded-md border-2 p-1">
											<div className="space-y-2 rounded-sm bg-yellow-50 p-2">
												<div className="space-y-2 rounded-md border border-yellow-100 bg-white p-2 shadow-xs">
													<div className="h-2 w-[80px] rounded-lg bg-yellow-200" />
													<div className="h-2 w-[100px] rounded-lg bg-yellow-200" />
												</div>
												<div className="flex items-center space-x-2 rounded-md border border-yellow-100 bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-yellow-500" />
													<div className="h-2 w-[100px] rounded-lg bg-yellow-200" />
												</div>
												<div className="flex items-center space-x-2 rounded-md border border-yellow-100 bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-yellow-300" />
													<div className="h-2 w-[100px] rounded-lg bg-yellow-200" />
												</div>
											</div>
										</div>
										<span className="block w-full p-2 text-center font-normal">
											Yellow
										</span>
									</FormLabel>
								</FormItem>
								<FormItem>
									<FormLabel className="[&:has([data-state=checked])>div]:border-primary">
										<FormControl>
											<RadioGroupItem value="violet" className="sr-only" />
										</FormControl>
										<div className="border-muted hover:border-accent items-center rounded-md border-2 p-1">
											<div className="space-y-2 rounded-sm bg-violet-50 p-2">
												<div className="space-y-2 rounded-md border border-violet-100 bg-white p-2 shadow-xs">
													<div className="h-2 w-[80px] rounded-lg bg-violet-200" />
													<div className="h-2 w-[100px] rounded-lg bg-violet-200" />
												</div>
												<div className="flex items-center space-x-2 rounded-md border border-violet-100 bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-violet-500" />
													<div className="h-2 w-[100px] rounded-lg bg-violet-200" />
												</div>
												<div className="flex items-center space-x-2 rounded-md border border-violet-100 bg-white p-2 shadow-xs">
													<div className="h-4 w-4 rounded-full bg-violet-300" />
													<div className="h-2 w-[100px] rounded-lg bg-violet-200" />
												</div>
											</div>
										</div>
										<span className="block w-full p-2 text-center font-normal">
											Violet
										</span>
									</FormLabel>
								</FormItem>
							</RadioGroup>
						</FormItem>
					)}
				/>

				<Button type="submit">Update preferences</Button>
			</form>
		</Form>
	);
}
