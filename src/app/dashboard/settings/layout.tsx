import {
	Icon<PERSON><PERSON>er<PERSON>he<PERSON>,
	IconNotification,
	<PERSON>conP<PERSON>te,
	IconTool,
	IconUser,
} from '@tabler/icons-react';
import { Separator } from '@/components/ui/separator';
import { Header } from '@/components/layout/header';
import { Main } from '@/components/layout/main';
import { ProfileDropdown } from '@/components/profile-dropdown';
import { ThemeSwitch } from '@/components/theme-switch';
import SidebarNav from '@/components/sidebar-nav';

export default function Layout({ children }: { children: React.ReactNode }) {
	return (
		<>
			{/* ===== Top Heading ===== */}
			<Header>
				<div className="ml-auto flex items-center space-x-4">
					<ThemeSwitch />
					<ProfileDropdown />
				</div>
			</Header>

			<Main fixed>
				<div className="space-y-0.5">
					<h1 className="text-2xl font-bold tracking-tight md:text-3xl">
						Settings
					</h1>
					<p className="text-muted-foreground">
						Manage your account settings and set e-mail preferences.
					</p>
				</div>
				<Separator className="my-4 lg:my-6" />
				<div className="flex flex-1 flex-col space-y-2 overflow-hidden md:space-y-2 lg:flex-row lg:space-y-0 lg:space-x-12">
					<aside className="top-0 lg:sticky lg:w-1/5">
						<SidebarNav items={sidebarNavItems} />
					</aside>
					{children}
				</div>
			</Main>
		</>
	);
}

const sidebarNavItems = [
	{
		title: 'Profile',
		icon: <IconUser size={18} />,
		href: '/dashboard/settings/profile',
	},
	{
		title: 'Appearance',
		icon: <IconPalette size={18} />,
		href: '/dashboard/settings/appearance',
	},
	{
		title: 'Account',
		icon: <IconTool size={18} />,
		href: '/dashboard/settings/account',
	},
	{
		title: 'Notifications',
		icon: <IconNotification size={18} />,
		href: '/dashboard/settings/notifications',
	},
	{
		title: 'Display',
		icon: <IconBrowserCheck size={18} />,
		href: '/dashboard/settings/display',
	},
];
