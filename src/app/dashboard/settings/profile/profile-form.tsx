'use client';
import { useForm } from 'react-hook-form';
import { showSubmittedData } from '@/utils/show-submitted-data';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { User } from '@/services/users/user';
import { useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';

export default function ProfileForm() {
	const { user } = useAuth();
	const form = useForm<User>({
		mode: 'onChange',
	});

	useEffect(() => {
		if (user) {
			form.reset(user);
		}
	}, [user]);

	return (
		<Form {...form}>
			<form
				onSubmit={form.handleSubmit((data) => showSubmittedData(data))}
				className="space-y-8">
				<FormField
					control={form.control}
					name="telegramId"
					render={({ field }) => (
						<FormItem>
							<FormLabel>ID</FormLabel>
							<FormControl>
								<Input disabled {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					control={form.control}
					name="username"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Username</FormLabel>
							<FormControl>
								<Input disabled {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					control={form.control}
					name="firstName"
					render={({ field }) => (
						<FormItem>
							<FormLabel>First Name</FormLabel>
							<FormControl>
								<Input disabled {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					control={form.control}
					name="lastName"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Last Name</FormLabel>
							<FormControl>
								<Input disabled {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					control={form.control}
					name="role"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Role</FormLabel>
							<FormControl>
								<Input disabled {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					control={form.control}
					name="credit"
					render={({ field }) => (
						<FormItem>
							<FormLabel>Credit</FormLabel>
							<FormControl>
								<Input disabled {...field} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</form>
		</Form>
	);
}
