import { Suspense } from 'react';
import { Head<PERSON> } from '@/components/layout/header';
import { Main } from '@/components/layout/main';
import { ProfileDropdown } from '@/components/profile-dropdown';
import { ThemeSwitch } from '@/components/theme-switch';
import CareersModule from '@/features/careers';

export default function CareersPage() {
	return (
		<>
			<Header>
				<div className="ml-auto flex items-center space-x-4">
					<ThemeSwitch />
					<ProfileDropdown />
				</div>
			</Header>

			<Main>
				<Suspense fallback={
					<div className="flex items-center justify-center py-8">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
					</div>
				}>
					<CareersModule />
				</Suspense>
			</Main>
		</>
	);
}
