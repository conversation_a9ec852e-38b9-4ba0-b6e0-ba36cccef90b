import type { <PERSON>ada<PERSON> } from 'next';
import {
	<PERSON>ra_Code,
	<PERSON>ei<PERSON>,
	<PERSON><PERSON><PERSON>_Mono,
	<PERSON>,
	<PERSON>rriweather,
	<PERSON><PERSON>,
} from 'next/font/google';
import './globals.css';
import { AuthProvider } from '@/context/AuthContext';
import ReactQueryProvider from '@/context/ReactQueryProvider';
import { ThemeProvider } from '@/context/ThemeContext';
import { FontProvider } from '@/context/FontContext';
import { Toaster } from '@/components/ui/sonner';

const geistSans = Geist({
	variable: '--font-geist-sans',
	subsets: ['latin'],
});

const geistMono = Geist_Mono({
	variable: '--font-geist-mono',
	subsets: ['latin'],
});

const inter = Inter({
	subsets: ['latin'],
	variable: '--font-inter',
	display: 'swap',
});

const roboto = Roboto({
	weight: ['400', '500', '700'],
	subsets: ['latin'],
	variable: '--font-roboto',
	display: 'swap',
});

const firaCode = Fira_Code({
	subsets: ['latin'],
	variable: '--font-fira-code',
	display: 'swap',
});

const merriweather = Merriweather({
	weight: ['400', '700'],
	subsets: ['latin'],
	variable: '--font-merriweather',
	display: 'swap',
});

export const metadata: Metadata = {
	title: 'Create Next App',
	description: 'Generated by create next app',
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="en">
			<body
				className={`${geistSans.variable} ${geistMono.variable} ${inter.variable} ${roboto.variable} ${firaCode.variable} ${merriweather.variable} no-scrollbar antialiased`}>
				<ReactQueryProvider>
					<ThemeProvider defaultMode="system" defaultTheme="default" storageKey="ui-theme">
						<FontProvider>
							<AuthProvider>
								{children}
								<Toaster />
							</AuthProvider>
						</FontProvider>
					</ThemeProvider>
				</ReactQueryProvider>
			</body>
		</html>
	);
}
