import { AuthHeader } from '@/components/layout/header/AuthHeader';
import Typography from '@/components/shared/Typography';
import { FaDiscord } from 'react-icons/fa';
import { Separator } from '@/components/shared/Separator/Separator';

export default function Layout({ children }: ChildrenProp) {
	return (
		<div
			className={'relative flex h-screen flex-col bg-black'}
			style={{
				backgroundImage: 'url(/assets/pages/login/images/bg.png)',
				backgroundSize: 'cover',
				backgroundPosition: 'center',
				backgroundRepeat: 'no-repeat',
			}}>
			<AuthHeader />
			{children}
			<div
				className={
					'absolute right-0 bottom-0 left-0 flex items-center justify-center gap-2 py-24 text-neutral-800'
				}>
				<Typography variant={'12px_f600_l18'}>Privacy Policy</Typography>
				<Separator orientation={'vertical'} className={'bg-neutral-800'} />
				<Typography variant={'12px_f600_l18'}>Terms & Condition</Typography>
				<Separator orientation={'vertical'} className={'bg-neutral-800'} />
				<div className={'flex items-center justify-center'}>
					<FaDiscord className={'text-neutral-800'} />
				</div>
			</div>
		</div>
	);
}
