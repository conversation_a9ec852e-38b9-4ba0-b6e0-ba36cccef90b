import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { API_URL } from '@/configs/global.config';

// Cache configuration
const CACHE_DURATION = 15 * 60 * 1000; // 15 minutes in milliseconds

// In-memory cache
interface CacheEntry {
	data: any;
	timestamp: number;
}

let cache: CacheEntry | null = null;

// Helper function to check if cache is valid
function isCacheValid(): boolean {
	if (!cache) return false;
	const now = Date.now();
	return now - cache.timestamp < CACHE_DURATION;
}

// Helper function to get auth headers from request
async function getAuthHeaders() {
	const cookieStore = await cookies();
	const token = cookieStore.get('access_token')?.value;

	return {
		'Content-Type': 'application/json',
		...(token && { Authorization: `Bearer ${token}` }),
	};
}

// Helper function to calculate statistics from API data
function calculateStats(files: any[]) {
	const totalFiles = files.length;

	// Flatten all short links from all files
	const allShortLinks = files.flatMap((file: any) =>
		file.fileShortLinkMeta.map((meta: any) => ({
			...meta,
			fileId: file._id,
			brand: file.brand,
			createdAt: file.createdAt,
			createdBy: file.createdBy,
			file: file.file,
		}))
	);

	const totalShortLinks = allShortLinks.length;

	// Calculate statistics
	const activated = allShortLinks.filter(
		(link: any) => link.shortLinkStatus === 'activated'
	).length;
	const deactivated = allShortLinks.filter(
		(link: any) => link.shortLinkStatus === 'deactivated'
	).length;

	const totalDownloads = allShortLinks.reduce(
		(sum: number, link: any) => sum + (link.downloadCount || 0),
		0
	);
	const totalViews = allShortLinks.reduce(
		(sum: number, link: any) => sum + (link.viewCount || 0),
		0
	);

	// Get recent files (last 10)
	const recentFiles = files
		.sort(
			(a: any, b: any) =>
				new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
		)
		.slice(0, 10);

	// Calculate top brands
	const brandStats = files.reduce((acc: any, file: any) => {
		if (!acc[file.brand]) {
			acc[file.brand] = {
				fileCount: 0,
				shortLinkCount: 0,
			};
		}
		acc[file.brand].fileCount += 1;
		acc[file.brand].shortLinkCount += file.fileShortLinkMeta.length;
		return acc;
	}, {});

	const topBrands = Object.entries(brandStats)
		.map(([brand, stats]: [string, any]) => ({
			brand,
			fileCount: stats.fileCount,
			shortLinkCount: stats.shortLinkCount,
		}))
		.sort((a, b) => b.shortLinkCount - a.shortLinkCount)
		.slice(0, 5);

	// Status distribution based on short links
	const statusDistribution = [
		{
			status: 'activated',
			count: activated,
			percentage:
				totalShortLinks > 0
					? Math.round((activated / totalShortLinks) * 100)
					: 0,
		},
		{
			status: 'deactivated',
			count: deactivated,
			percentage:
				totalShortLinks > 0
					? Math.round((deactivated / totalShortLinks) * 100)
					: 0,
		},
	];

	return {
		totalFiles,
		totalShortLinks,
		activated,
		deactivated,
		totalDownloads,
		totalViews,
		recentFiles,
		topBrands,
		statusDistribution,
	};
}

// Helper function to fetch data from external API
async function fetchFileShortLinksData() {
	const headers = await getAuthHeaders();
	const response = await fetch(`${API_URL}/file-short-links?limit=1000`, {
		headers,
		// Don't cache the external API call
		next: {
			revalidate: CACHE_DURATION / 1000, // Revalidate every 15 minutes
		},
	});

	if (!response.ok) {
		throw new Error(
			`Failed to fetch file short links: ${response.status} ${response.statusText}`
		);
	}

	const data = await response.json();
	return data.data || [];
}

export async function GET() {
	try {
		// Check if we have valid cached data
		if (isCacheValid() && cache) {
			console.log('📊 Returning cached file short link stats');
			const cacheAge = Math.floor((Date.now() - cache.timestamp) / 1000);
			const nextUpdate = Math.floor(
				(CACHE_DURATION - (Date.now() - cache.timestamp)) / 1000
			);

			return NextResponse.json({
				...cache.data,
				cached: true,
				cacheAge,
				nextUpdate,
			});
		}

		console.log('🔄 Calculating fresh file short link stats...');

		// Fetch fresh data from external API
		const files = await fetchFileShortLinksData();

		// Calculate statistics
		const stats = calculateStats(files);

		// Update cache
		cache = {
			data: {
				...stats,
				cached: false,
				cacheAge: 0,
				nextUpdate: CACHE_DURATION / 1000,
			},
			timestamp: Date.now(),
		};

		console.log('✅ File short link stats calculated and cached');

		return NextResponse.json(cache.data);
	} catch (error) {
		console.error('❌ Error in file short link stats API:', error);

		// If we have stale cache data, return it with error flag
		if (cache) {
			const cacheAge = Math.floor((Date.now() - cache.timestamp) / 1000);
			console.log('⚠️ Returning stale cached data due to error');

			return NextResponse.json({
				...cache.data,
				cached: true,
				stale: true,
				error: 'Failed to fetch fresh data, returning cached data',
				cacheAge,
				nextUpdate: 0,
			});
		}

		return NextResponse.json(
			{
				error: 'Failed to fetch file short link statistics',
				message: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

// Manual cache refresh endpoint
export async function POST() {
	try {
		console.log('🔄 Manual cache refresh requested');

		// Force fetch fresh data
		const files = await fetchFileShortLinksData();
		const stats = calculateStats(files);

		// Update cache
		cache = {
			data: {
				...stats,
				cached: false,
				cacheAge: 0,
				nextUpdate: CACHE_DURATION / 1000,
			},
			timestamp: Date.now(),
		};

		console.log('✅ Cache refreshed successfully');

		return NextResponse.json({
			message: 'Cache refreshed successfully',
			...cache.data,
		});
	} catch (error) {
		console.error('❌ Error refreshing cache:', error);
		return NextResponse.json(
			{
				error: 'Failed to refresh cache',
				message: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

// Clear cache endpoint
export async function DELETE() {
	try {
		cache = null;
		console.log('🗑️ Cache cleared successfully');

		return NextResponse.json({
			message: 'Cache cleared successfully',
		});
	} catch (error) {
		console.error('❌ Error clearing cache:', error);
		return NextResponse.json(
			{ error: 'Failed to clear cache' },
			{ status: 500 }
		);
	}
}
