import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
	try {
		const { url } = await request.json();

		if (!url) {
			return NextResponse.json(
				{ error: 'URL is required' },
				{ status: 400 }
			);
		}

		const res = await fetch('https://x54.run/tinyurl', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({ url }),
		});

		// Kiểm tra status trước khi parse JSON
		if (!res.ok) {
			const text = await res.text();
			console.error(`[TinyURL] Server Error ${res.status}: ${text}`);
			return NextResponse.json(
				{ error: `TinyURL API error: ${res.status}` },
				{ status: res.status }
			);
		}

		// Thử parse JSON và bắt lỗi nếu JSON không hợp lệ
		try {
			const json = await res.json();
			return NextResponse.json(json);
		} catch (jsonError) {
			console.error('[TinyURL] ❌ Lỗi parse JSON:', jsonError);
			const fallback = await res.text();
			console.error('[TinyURL] ❗ Fallback raw response:', fallback);
			return NextResponse.json(
				{ error: 'Invalid JSON response from TinyURL API' },
				{ status: 500 }
			);
		}
	} catch (err) {
		console.error('[TinyURL] ❌ Fetch failed:', err);
		return NextResponse.json(
			{ error: 'Internal server error' },
			{ status: 500 }
		);
	}
}
