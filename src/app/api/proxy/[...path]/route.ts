import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
	return await handleProxy(request);
}

export async function POST(request: NextRequest) {
	return await handleProxy(request);
}

export async function PUT(request: NextRequest) {
	return await handleProxy(request);
}

export async function DELETE(request: NextRequest) {
	return await handleProxy(request);
}

export async function PATCH(request: NextRequest) {
	return await handleProxy(request);
}

const handleProxy = async (request: NextRequest) => {
	try {
		// Extract path after /api/proxy/
		const regex = /^https?:\/\/[^/]+\/api\/proxy\//;
		const url = new URL(request.url).toString().replace(regex, '');

		// Get API endpoint from X-Original-API header or fallback to environment variable
		const originalApi =
			request.headers.get('X-Original-API') || process.env.NEXT_PUBLIC_URL_VNBK;
		const endpoint = `${originalApi}/${url}`;

		// Parse request body
		let req;
		if (request.method !== 'GET') {
			req = await request.json().catch(() => ({}));
		}

		// Forward the request to the target API
		const res = await fetch(endpoint, {
			method: request.method,
			cache: 'no-cache',
			headers: {
				'Content-Type': 'application/json',
			},
			...(request.method !== 'GET' && { body: JSON.stringify(req) }),
		});

		// Return the API response
		const data = await res.json();
		return NextResponse.json(data);
	} catch (error) {
		const err = error as Error;
		return NextResponse.json(
			{
				error: err.message,
			},
			{
				status: 500,
			}
		);
	}
};
