import * as React from 'react';
import { SVGProps } from 'react';
import { cn } from '@/lib/utils';
import Image from 'next/image';

const Logo = ({
	opts,
	icon,
	className,
}: {
	className?: string;
	icon?: boolean;
	opts?: {
		iconSize?: number;
		text?: SVGProps<SVGSVGElement>;
	};
}) => {
	return (
		<div className={cn('flex items-center', className)}>
			{icon && (
				<Image
					src={'/assets/logo/wolf-logo.png'}
					alt={'wolf-logo'}
					width={opts?.iconSize ?? 48}
					height={opts?.iconSize ?? 48}
					className={'aspect-square object-contain'}
				/>
			)}
			<svg
				xmlns="http://www.w3.org/2000/svg"
				data-name="Layer 2"
				viewBox="0 0 85.05 38.04"
				width={opts?.text?.width || 50}
				height={opts?.text?.height || 50}
				{...opts?.text}>
				<g data-name="Layer 1">
					<path
						fill={opts?.text?.color || 'white'}
						d="M3.5 32.7C.81 27.9-.22 23.53.04 17.59.1 8.42 5.01-.18 15.26 0c2.42-.04 4.81.39 6.98 1.37.43.19.9.34 1.31.07.58-.29.81-1.32 1.56-1.2.81.34 3.01 1.57 3.71 2.21.4.66-1.23 2.2-1.13 2.95.26 1.55 1.51 2.84 1.9 4.41 2.43 7.03 1.88 17.69-2.44 23.03-3.91 5.34-12.27 6.42-18.4 3.92-.54-.18-1.02 0-1.38.38-.28.27-.53.66-.96.59-.76-.25-2.97-1.71-3.63-2.18-.75-.64.86-1.97.74-2.7v-.14Zm3.98-13.6c0 2.02.18 4.08.68 6.02.06.18.11.52.26.44.9-1.35 9.82-15.33 11.21-17.49.53-.72-2.01-1.47-2.67-1.6-7.33-1.2-9.65 5.87-9.49 12.42v.21Zm16.04-.17c-.02-2-.1-4.11-.68-6-.05-.13-.1-.15-.16-.1-.89 1.36-9.63 15.22-11.01 17.38-.54.69 1.93 1.27 2.55 1.38 7.31.98 9.43-5.86 9.29-12.45v-.21ZM40.05 23.88c-1.2-2.38-6.03-9.65-7.39-11.99-.11-.22-.28-.42-.12-.58 1.64-.29 5.22-.06 6.77-.11.47.02.9.06 1.25.38 1.86 2.37 3.35 5.25 4.47 7.77.77 1.33 1.73-2.49 2.19-2.88.67-1.14 1.89-2.98 2.71-4.22.68-1.05 1.15-1.08 2.76-1.07h3.78c2.18-.04 1.34.3.24 2.18-1.89 2.94-4.03 6.29-5.95 9.27-.79 1.2-.67 1.05.21 2.47 1.01 1.6 2.04 3.23 3.08 4.88 1.08 1.71 2.44 3.86 3.4 5.39.59.99 1.07 1.55.91 1.74-1.57.34-5.59.08-6.9.12-.77-.02-1.18-.48-1.59-1.12-.89-1.36-2.32-3.5-3.06-4.71-.45-.73-.88-1.87-1.28-2.77-.17-.31-.38-.92-.75-.74-.73.77-1.09 2.3-1.62 3.11-1 1.65-2.79 4.45-3.77 5.74-.84.79-1.74.43-2.99.52-1.28-.11-3.56.24-4.55-.18 1.02-2.37 6.64-10.01 8.21-13.15v-.06ZM70.56 9.21c-1.03 1.32-2.62 2.36-4.22 3.4-.46.28-.97.62-1.46.52-1.05-.42-2.06-2.15-2.58-3.08-.11-.32-.03-.52.2-.75 1.65-1.34 8.35-6.75 11.14-8.83.97-.51 3.41-.37 4-.17.23.12.2.35.21.59.02 4.05-.04 28.09.03 30.33.06.28.41.31 1.46.32 1.26.02 3.53-.04 4.58.04 1 .07 1.11.66 1.13 1.82 0 .99.02 2.26-.02 2.94-.06.57-.07.84-.53.92-2.58.02-16.01.06-20 0-.36-.03-.47-.31-.51-.65-.05-.4-.05-1.07-.05-1.85.06-1.11-.17-2 .23-2.86l.07-.08c.2-.2.5-.23.8-.26 1.53-.09 4.55.08 5.79-.09.78-.2.36-1.18.47-1.9"
					/>
				</g>
			</svg>
		</div>
	);
};
export default Logo;
