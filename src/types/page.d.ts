import { Applicant } from '@/types/applicant';
import { Domain } from '@/types/domain';

export interface PageDetail {
	_id: string;
	title: string;
	description: string;
	applicants: Applicant[];
	createdAt: string;
}

export interface Page {
	_id: string;
	title: string;
	description?: string;
	domain: Omit<Domain, 'totalPages' | 'updatedAt'>;
	totalJobs?: number;
	code: string;
	createdAt: string;
}
