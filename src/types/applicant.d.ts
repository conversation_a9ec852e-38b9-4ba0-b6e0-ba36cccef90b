import { Page } from '@/types/page';
import { Job } from '@/types/job';

export interface Applicant {
	job?: Omit<Job, 'totalApplicants' | 'page'>;
	fullName: string;
	email: string;
	phone: string;
	address: string;
	resume: Blob;
	createdAt: string;
	createdBy: string;
	_id: string;
	page: Omit<Page, 'totalJobs' | 'domain'>;
	clientInfo?: ClientInfo;
}

export interface ClientInfo {
	ip: string;
	userAgent: string;
	country: string;
}
