export interface Email {
	_id: string;
	email: string;
	password: string;
	title?: string;
	description?: string;
	createdAt: string;
}

export interface EmailResult {
	email: string;
	status: string;
	shortLink: string | null;
	error: string | null;
}

export interface EmailItem {
	email: string;
	shortLinkId: string;
	shortLinkStatus: 'activated' | 'deactivated';
	countryCode?: string;
	popup?: boolean;
}

export interface AddEmailResponse {
	emails: EmailItem[];
	quantitySuccess: number;
	file: string;
	logo: string;
	prefixPass: string;
	domainId: string;
	duplicateEmails: string[];
	createdAt: string;
}
