// Domain types
export interface Domain {
	_id: string;
	name: string;
	description?: string;
	createdAt: string;
	createdBy: string;
	updatedAt?: string;
	updatedBy?: string;
}

export interface CreateDomainDTO {
	name: string;
	description?: string;
}

// Page types
export interface Page {
	_id: string;
	title: string;
	description?: string;
	domainId: string;
	status: 'activated' | 'deactivated';
	createdAt: string;
	createdBy: string;
	updatedAt?: string;
	updatedBy?: string;
}

export interface PageDetail extends Page {
	domain?: Domain;
}

export interface CreatePageDTO {
	title: string;
	description?: string;
	domainId: string;
}

export interface UpdatePageDTO {
	title?: string;
	description?: string;
	domainId?: string;
}

// Job types
export interface Job {
	_id: string;
	title: string;
	description?: string;
	content: string; // Rich text content from Tiptap
	address: string;
	pageCode: string;
	pageId?: string;
	tags?: string[];
	status: 'activated' | 'deactivated';
	createdAt: string;
	createdBy: string;
	updatedAt?: string;
	updatedBy?: string;
}

export interface JobDetail extends Job {
	page?: Page;
}

export interface CreateJobDTO {
	title: string;
	description?: string;
	content: string;
	address: string;
	pageCode: string;
	tags?: string[];
}

export interface EditJobDTO {
	title?: string;
	description?: string;
	content?: string;
	address?: string;
	pageId?: string;
	tags?: string[];
}

// Applicant types
export interface Applicant {
	_id: string;
	email: string;
	fullName: string;
	phone?: string;
	jobId: string;
	pageId?: string;
	pageCode?: string;
	createdBy?: string;
	startedDate?: string;
	endedDate?: string;
	tag?: string;
	createdAt: string;
	updatedAt?: string;
}

export interface ApplicantDetail extends Applicant {
	job?: Job;
	page?: Page;
}

export interface CreateApplicantDTO {
	email: string;
	fullName: string;
	phone?: string;
	jobId: string;
	pageId?: string;
	pageCode?: string;
	tag?: string;
}

// Statistics types
export interface CareersStats {
	totalDomains: number;
	totalPages: number;
	totalJobs: number;
	totalApplicants: number;
	activeJobs: number;
	inactiveJobs: number;
	recentApplicants: ApplicantDetail[];
}

export interface JobStats {
	jobId: string;
	jobTitle: string;
	totalApplicants: number;
	recentApplicants: number;
	applicantsByMonth: {
		month: string;
		count: number;
	}[];
}

export interface PageStats {
	pageId: string;
	pageTitle: string;
	totalJobs: number;
	totalApplicants: number;
	jobStats: JobStats[];
}

export interface DomainStats {
	domainId: string;
	domainName: string;
	totalPages: number;
	totalJobs: number;
	totalApplicants: number;
	pageStats: PageStats[];
}

// Filter types
export interface JobFilters {
	search?: string;
	createdBy?: string;
	pageId?: string;
	pageCode?: string;
	status?: 'activated' | 'deactivated';
	skip?: number;
	limit?: number;
}

export interface ApplicantFilters {
	email?: string;
	fullName?: string;
	phone?: string;
	createdBy?: string;
	pageId?: string;
	pageCode?: string;
	jobId?: string;
	startedDate?: string;
	endedDate?: string;
	tag?: string;
	skip?: number;
	limit?: number;
}

export interface PageFilters {
	search?: string;
	createdBy?: string;
	domainId?: string;
	status?: 'activated' | 'deactivated';
	skip?: number;
	limit?: number;
}

export interface DomainFilters {
	search?: string;
	createdBy?: string;
	skip?: number;
	limit?: number;
}
