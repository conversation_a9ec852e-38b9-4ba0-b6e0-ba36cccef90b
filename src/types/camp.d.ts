export interface CampTableItem {
	TINYURL: string;
	EMAIL: string;
	COUNTRY: string;
	FULLNAME: string;
	JOB: string;
}

// Mail template email structure
export interface MailTemplateEmail {
	subject: string;
	content: string;
	delay: number;
}

// Complete mail template structure
export interface MailTemplate {
	_id: string;
	sender: string;
	senderName: string;
	brand: string;
	startHTML: MailTemplateEmail;
	otherHTML: MailTemplateEmail[]; // Exactly 2 items: remind1 and remind2
	endHTML: MailTemplateEmail;
	createdAt: string;
	createdBy: string;
	updatedAt?: string;
	updatedBy?: string;
}

// Camp interface for list view (minimal webhook and mail info)
export interface Camp {
	_id: string;
	brand: string;
	webhook: {
		_id: string;
	};
	startDate: string; // ISO date string
	file: string;
	mail: string; // Just ID string in list view
	createdAt: string;
	createdBy: string;
	updatedAt?: string;
	updatedBy?: string;
}

// Camp detail interface with full webhook and mail info
export interface CampDetail {
	_id: string;
	brand: string;
	webhook: {
		_id: string;
		webhookUrl: string;
		name: string;
		description?: string;
		createdAt: string;
		createdBy: string;
	};
	startDate: string; // ISO date string
	file: string;
	mail: MailTemplate; // Full mail template object in detail view
	createdAt: string;
	createdBy: string;
	updatedAt?: string;
	updatedBy?: string;
}

// Create Camp DTO (matches Swagger CreateCampDTO)
export interface CreateCampDTO {
	brand: string; // Brand name
	webhook: string; // Webhook ID for notifications
	startDate: string; // Campaign start date (ISO string)
	mail: string; // Mail template ID (required)
}

// Update Camp DTO (matches Swagger UpdateCampDTO)
export interface UpdateCampDTO {
	brand?: string; // Brand name
	webhook?: string; // Webhook ID for notifications
	startDate?: string; // Campaign start date (ISO string)
	mail?: string; // Mail template ID
}

// Mail template reference for camps (simplified for dropdowns)
export interface CampMailTemplate {
	_id: string;
	sender: string;
	senderName: string;
	brand: string;
	createdAt: string;
}

// Campaign Analytics Types (from Swagger)
export interface CampaignOverview {
	campaignId: string;
	brand: string;
	startDate: string;
	totalUsers: number;
	totalEmailsSent: number;
	successRate: number;
	duration: string;
}

export interface EmailPerformanceMetrics {
	sent: number;
	opened: number;
	openRate: number;
	clicked: number;
	clickRate: number;
	converted?: number; // Only for end email
	conversionRate?: number; // Only for end email
}

export interface EmailPerformance {
	start: EmailPerformanceMetrics;
	end: EmailPerformanceMetrics;
}

export interface UserSegmentMetrics {
	position: string;
	totalUsers: number;
	openRate: number;
	clickRate: number;
	conversionRate: number;
}

export interface UserSegmentAnalysis {
	byPosition: UserSegmentMetrics[];
}

export interface AvgTimeToOpen {
	start: string;
	'other[0]': string;
	'other[1]': string;
	'other[2]': string;
	'other[3]': string;
	'other[4]': string;
	end: string;
}

export interface TrendData {
	date: string;
	opens?: number;
	conversions?: number;
}

export interface TimeAnalysis {
	avgTimeToOpen: AvgTimeToOpen;
	avgTimeToConvert: string;
	openTrend: TrendData[];
	conversionTrend: TrendData[];
}
