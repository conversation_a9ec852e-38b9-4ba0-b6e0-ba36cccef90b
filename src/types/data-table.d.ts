import { ColumnDef } from '@tanstack/react-table';

export type TablePagination = {
	pageIndex: number;
	pageSize: number;
	totalPage: number;
};

export type ColumnDefWithClassName<Data> = {
	headerClassName?: string;
} & ColumnDef<Data>;

export interface DataTableProps<Data> {
	columns: ColumnDefWithClassName<Data>[];
	data: Data[];
	pagination: TablePagination;
	onPaginationChange?: (val: TablePagination) => void;
}
