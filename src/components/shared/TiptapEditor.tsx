'use client';

import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Button } from '@/components/ui/button';
import {
	Bold,
	Heading1,
	Heading2,
	Heading3,
	Italic,
	List,
	ListOrdered,
	Quote,
	Redo,
	Undo,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface TiptapEditorProps {
	content: string;
	onChange: (content: string) => void;
	placeholder?: string;
	className?: string;
	editable?: boolean;
}

export const TiptapEditor = ({
	content,
	onChange,
	placeholder = 'Nhập nội dung...',
	className,
	editable = true,
}: TiptapEditorProps) => {
	const editor = useEditor({
		extensions: [
			StarterKit.configure({
				bulletList: {
					keepMarks: true,
					keepAttributes: false,
				},
				orderedList: {
					keepMarks: true,
					keepAttributes: false,
				},
			}),
		],
		content,
		editable,
		onUpdate: ({ editor }) => {
			onChange(editor.getHTML());
		},
		editorProps: {
			attributes: {
				class: cn(
					'prose prose-sm sm:prose-base lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[200px] p-4',
					className
				),
			},
		},
	});

	if (!editor) {
		return null;
	}

	if (!editable) {
		return (
			<div className={cn('prose prose-sm sm:prose-base max-w-none', className)}>
				<EditorContent editor={editor} />
			</div>
		);
	}

	return (
		<div className="overflow-hidden rounded-lg border border-gray-300">
			{/* Toolbar */}
			<div className="flex flex-wrap gap-1 border-b border-gray-300 p-2">
				<Button
					type="button"
					variant="ghost"
					size="sm"
					onClick={() => editor.chain().focus().toggleBold().run()}
					className={editor.isActive('bold') ? 'bg-gray-200 text-black' : ''}>
					<Bold className="h-4 w-4" />
				</Button>

				<Button
					type="button"
					variant="ghost"
					size="sm"
					onClick={() => editor.chain().focus().toggleItalic().run()}
					className={editor.isActive('italic') ? 'bg-gray-200 text-black' : ''}>
					<Italic className="h-4 w-4" />
				</Button>

				<Button
					type="button"
					variant="ghost"
					size="sm"
					onClick={() =>
						editor.chain().focus().toggleHeading({ level: 1 }).run()
					}
					className={
						editor.isActive('heading', { level: 1 }) ? 'bg-gray-200 text-black' : ''
					}>
					<Heading1 className="h-4 w-4" />
				</Button>

				<Button
					type="button"
					variant="ghost"
					size="sm"
					onClick={() =>
						editor.chain().focus().toggleHeading({ level: 2 }).run()
					}
					className={
						editor.isActive('heading', { level: 2 }) ? 'bg-gray-200 text-black' : ''
					}>
					<Heading2 className="h-4 w-4" />
				</Button>

				<Button
					type="button"
					variant="ghost"
					size="sm"
					onClick={() =>
						editor.chain().focus().toggleHeading({ level: 3 }).run()
					}
					className={
						editor.isActive('heading', { level: 3 }) ? 'bg-gray-200 text-black' : ''
					}>
					<Heading3 className="h-4 w-4" />
				</Button>

				<Button
					type="button"
					variant="ghost"
					size="sm"
					onClick={() => editor.chain().focus().toggleBulletList().run()}
					className={editor.isActive('bulletList') ? 'bg-gray-200 text-black' : ''}>
					<List className="h-4 w-4" />
				</Button>

				<Button
					type="button"
					variant="ghost"
					size="sm"
					onClick={() => editor.chain().focus().toggleOrderedList().run()}
					className={editor.isActive('orderedList') ? 'bg-gray-200 text-black' : ''}>
					<ListOrdered className="h-4 w-4" />
				</Button>

				<Button
					type="button"
					variant="ghost"
					size="sm"
					onClick={() => editor.chain().focus().toggleBlockquote().run()}
					className={editor.isActive('blockquote') ? 'bg-gray-200 text-black' : ''}>
					<Quote className="h-4 w-4" />
				</Button>

				<div className="mx-1 h-6 w-px bg-gray-300" />

				<Button
					type="button"
					variant="ghost"
					size="sm"
					onClick={() => editor.chain().focus().undo().run()}
					disabled={!editor.can().undo()}>
					<Undo className="h-4 w-4" />
				</Button>

				<Button
					type="button"
					variant="ghost"
					size="sm"
					onClick={() => editor.chain().focus().redo().run()}
					disabled={!editor.can().redo()}>
					<Redo className="h-4 w-4" />
				</Button>
			</div>

			{/* Editor Content */}
			<div className="min-h-[200px]">
				<EditorContent editor={editor} placeholder={placeholder} />
			</div>
		</div>
	);
};
