'use server';

import { CreateLinkParams, DubLink } from '@/types/dub';
import { DubClient } from '@/lib/dub/client';

const getDubClient = () => {
	const apiKey = process.env.DUB_API_KEY;

	if (!apiKey) {
		throw new Error('DUB_API_KEY không được cấu hình');
	}

	return new DubClient(apiKey);
};

/**
 * Tạo một link rút gọn mới
 */
export async function createDubLink(data: CreateLinkParams): Promise<DubLink> {
	const client = getDubClient();

	try {
		return await client.createLink(data);
	} catch (error) {
		console.error(error);
		throw new Error('Không thể tạo link rút gọn');
	}
}

/**
 * Lấy tất cả các link
 */
export async function getLinks(params?: {
	limit?: number;
	page?: number;
	tagId?: string;
	search?: string;
	sort?: string;
	archived?: boolean;
}) {
	const client = getDubClient();

	try {
		return await client.getLinks(params);
	} catch (error) {
		console.error('Error fetching links:', error);
		throw new Error('Không thể lấy danh sách link');
	}
}

/**
 * Lấy thông tin về một link cụ thể
 */
export async function getLinkById(linkId: string): Promise<DubLink> {
	const client = getDubClient();

	try {
		return await client.getLink(linkId);
	} catch (error) {
		console.error(`Error fetching link ${linkId}:`, error);
		throw new Error('Không thể lấy thông tin link');
	}
}

/**
 * Xóa một link
 */
export async function deleteLink(linkId: string): Promise<void> {
	const client = getDubClient();

	try {
		await client.deleteLink(linkId);
	} catch (error) {
		console.error(`Error deleting link ${linkId}:`, error);
		throw new Error('Không thể xóa link');
	}
}

/**
 * Cập nhật thông tin một link
 */
export async function updateLink(
	linkId: string,
	data: Partial<CreateLinkParams>
): Promise<DubLink> {
	const client = getDubClient();

	try {
		return await client.updateLink(linkId, data);
	} catch (error) {
		console.error(`Error updating link ${linkId}:`, error);
		throw new Error('Không thể cập nhật link');
	}
}
