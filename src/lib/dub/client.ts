import { CreateLinkParams, DubLink } from '@/types/dub';

export class DubClient {
	private readonly apiKey: string;
	private baseUrl: string = 'https://api.dub.co';

	constructor(apiKey: string) {
		this.apiKey = apiKey;
	}

	private async request<T>(
		endpoint: string,
		options: RequestInit = {}
	): Promise<T> {
		const url = `${this.baseUrl}${endpoint}`;
		const headers = {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${this.apiKey}`,
			...options.headers,
		};

		const response = await fetch(url, {
			...options,
			headers,
		});

		if (!response.ok) {
			const error = await response.json();
			throw new Error(error);
		}

		return response.json();
	}

	/**
	 * Create a new short link
	 */
	async createLink(params: CreateLinkParams): Promise<DubLink> {
		return this.request<DubLink>('/links', {
			method: 'POST',
			body: JSON.stringify(params),
		});
	}

	/**
	 * Get a link by ID or key
	 */
	async getLink(linkId: string): Promise<DubLink> {
		return this.request<DubLink>(`/links/${linkId}`);
	}

	/**
	 * Get analytics for a link
	 */
	async getLinkAnalytics(linkId: string): Promise<any> {
		return this.request<any>(`/links/${linkId}/analytics`);
	}

	/**
	 * Update a link
	 */
	async updateLink(
		linkId: string,
		params: Partial<CreateLinkParams>
	): Promise<DubLink> {
		return this.request<DubLink>(`/links/${linkId}`, {
			method: 'PATCH',
			body: JSON.stringify(params),
		});
	}

	/**
	 * Delete a link
	 */
	async deleteLink(linkId: string): Promise<void> {
		await this.request(`/links/${linkId}`, {
			method: 'DELETE',
		});
	}

	/**
	 * Get all links for the workspace
	 */
	async getLinks(params?: {
		limit?: number;
		page?: number;
		tagId?: string;
		search?: string;
		sort?: string;
		archived?: boolean;
	}): Promise<{ links: DubLink[]; totalLinks: number }> {
		const queryParams = new URLSearchParams();

		if (params) {
			Object.entries(params).forEach(([key, value]) => {
				if (value !== undefined) {
					queryParams.append(key, value.toString());
				}
			});
		}

		const query = queryParams.toString() ? `?${queryParams.toString()}` : '';
		return this.request<{ links: DubLink[]; totalLinks: number }>(
			`/links${query}`
		);
	}
}
