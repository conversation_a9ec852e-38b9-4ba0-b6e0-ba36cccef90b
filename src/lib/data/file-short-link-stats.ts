import { cookies } from 'next/headers';
import { API_URL } from '@/configs/global.config';
import { FileShortLinkStats } from '@/services/file-short-link/file-short-link';

// Helper function to get auth headers from cookies
async function getAuthHeaders() {
	const cookieStore = await cookies();
	const token = cookieStore.get('access_token')?.value;

	return {
		'Content-Type': 'application/json',
		...(token && { Authorization: `Bearer ${token}` }),
	};
}

// Helper function to calculate statistics from API data
function calculateStats(files: any[]): FileShortLinkStats {
	const totalFiles = files.length;

	// Flatten all short links from all files
	const allShortLinks = files.flatMap((file: any) =>
		file.fileShortLinkMeta.map((meta: any) => ({
			...meta,
			fileId: file._id,
			brand: file.brand,
			createdAt: file.createdAt,
			createdBy: file.createdBy,
			file: file.file,
		}))
	);

	const totalShortLinks = allShortLinks.length;

	// Calculate statistics
	const activated = allShortLinks.filter(
		(link: any) => link.shortLinkStatus === 'activated'
	).length;
	const deactivated = allShortLinks.filter(
		(link: any) => link.shortLinkStatus === 'deactivated'
	).length;

	const totalDownloads = allShortLinks.reduce(
		(sum: number, link: any) => sum + (link.downloadCount || 0),
		0
	);
	const totalViews = allShortLinks.reduce(
		(sum: number, link: any) => sum + (link.viewCount || 0),
		0
	);

	// Get recent files (last 10)
	const recentFiles = files
		.sort(
			(a: any, b: any) =>
				new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
		)
		.slice(0, 10);

	// Calculate top brands
	const brandStats = files.reduce((acc: any, file: any) => {
		if (!acc[file.brand]) {
			acc[file.brand] = {
				fileCount: 0,
				shortLinkCount: 0,
			};
		}
		acc[file.brand].fileCount += 1;
		acc[file.brand].shortLinkCount += file.fileShortLinkMeta.length;
		return acc;
	}, {});

	const topBrands = Object.entries(brandStats)
		.map(([brand, stats]: [string, any]) => ({
			brand,
			fileCount: stats.fileCount,
			shortLinkCount: stats.shortLinkCount,
		}))
		.sort((a, b) => b.shortLinkCount - a.shortLinkCount)
		.slice(0, 5);

	// Status distribution based on short links
	const statusDistribution = [
		{
			status: 'activated',
			count: activated,
			percentage:
				totalShortLinks > 0
					? Math.round((activated / totalShortLinks) * 100)
					: 0,
		},
		{
			status: 'deactivated',
			count: deactivated,
			percentage:
				totalShortLinks > 0
					? Math.round((deactivated / totalShortLinks) * 100)
					: 0,
		},
	];

	return {
		totalFiles,
		totalShortLinks,
		activated,
		deactivated,
		totalDownloads,
		totalViews,
		recentFiles,
		topBrands,
		statusDistribution,
	};
}

/**
 * Server-side function to fetch file short link statistics
 * Uses Next.js built-in fetch caching with revalidation
 */
export async function getFileShortLinkStats(): Promise<FileShortLinkStats> {
	try {
		const headers = await getAuthHeaders();
		
		// Use Next.js fetch with caching and revalidation
		// Cache for 15 minutes (900 seconds)
		const response = await fetch(`${API_URL}/file-short-links?limit=1000`, {
			headers,
			next: { 
				revalidate: 900, // 15 minutes
				tags: ['file-short-link-stats'] // Tag for manual revalidation
			},
		});

		if (!response.ok) {
			throw new Error(
				`Failed to fetch file short links: ${response.status} ${response.statusText}`
			);
		}

		const data = await response.json();
		const files = data.data || [];

		// Calculate and return statistics
		return calculateStats(files);
	} catch (error) {
		console.error('❌ Error fetching file short link stats:', error);
		throw new Error('Failed to fetch file short link statistics');
	}
}

/**
 * Server action to manually revalidate the cache
 */
export async function revalidateFileShortLinkStats() {
	const { revalidateTag } = await import('next/cache');
	revalidateTag('file-short-link-stats');
}
