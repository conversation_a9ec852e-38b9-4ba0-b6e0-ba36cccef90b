export class N8nWebhookClient {
	private readonly webhookUrl: string;
	private readonly apiKey: string;

	constructor(webhookUrl: string, apiKey: string) {
		this.webhookUrl = webhookUrl;
		this.apiKey = apiKey;
	}

	async triggerWebhook(payload: Record<string, any>) {
		const res = await fetch(this.webhookUrl, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${this.apiKey}`,
			},
			body: JSON.stringify(payload),
		});

		if (!res.ok) {
			const errorData = await res.json().catch(() => ({}));
			throw new Error(
				`Webhook error ${res.status}: ${JSON.stringify(errorData)}`
			);
		}

		return await res.json();
	}
}
