import { N8nWebhookClient } from '@/lib/n8n/client';

const getN8nClient = () => {
	const webhookUrl = process.env.N8N_WEBHOOK_URL;
	const apiKey = process.env.N8N_API_KEY;

	if (!webhookUrl || !apiKey) {
		throw new Error('N8N_WEBHOOK_URL hoặc N8N_API_KEY không được cấu hình');
	}

	return new N8nWebhookClient(webhookUrl, apiKey);
};

export async function triggerN8nWebhook(payload: Record<string, any>) {
	const client = getN8nClient();

	try {
		await client.triggerWebhook(payload);
	} catch (error) {
		console.error('Error triggering N8n webhook:', error);
		throw new Error('Không thể kích hoạt webhook N8n');
	}
}
