import { CallAPI } from '@/configs/axios/axios';
import { TJob } from '@/features/careers/schemas/job-schema';
import { JobFilters } from '@/types/careers';

export const getJobs = async ({
	search,
	createdBy,
	pageId,
	pageCode,
	status,
	skip = 0,
	limit = 10,
}: JobFilters = {}): Promise<DataAPIResponse<TJob>> => {
	const queryParams = new URLSearchParams();
	if (search) queryParams.append('search', search);
	if (createdBy) queryParams.append('createdBy', createdBy);
	if (pageId) queryParams.append('pageId', pageId);
	if (pageCode) queryParams.append('pageCode', pageCode);
	if (status) queryParams.append('status', status);
	if (skip) queryParams.append('skip', skip.toString());
	if (limit) queryParams.append('limit', limit.toString());

	const { data, status: responseStatus } = await CallAPI({apiKey:true}).get(
		'/jobs?' + queryParams.toString()
	);

	if (responseStatus !== 200) {
		throw new Error('Failed to fetch jobs');
	}

	return data;
};
