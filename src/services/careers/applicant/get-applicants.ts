import { CallAPI } from '@/configs/axios/axios';
import { TApplicantDetail } from '@/features/careers/schemas/applicant-schema';
import { ApplicantFilters } from '@/types/careers';

export const getApplicants = async ({
	email,
	fullName,
	phone,
	createdBy,
	pageId,
	pageCode,
	jobId,
	startedDate,
	endedDate,
	tag,
	skip = 0,
	limit,
}: ApplicantFilters = {}): Promise<DataAPIResponse<TApplicant>> => {
	const queryParams = new URLSearchParams();
	if (email) queryParams.append('email', email);
	if (fullName) queryParams.append('fullName', fullName);
	if (phone) queryParams.append('phone', phone);
	if (createdBy) queryParams.append('createdBy', createdBy);
	if (pageId) queryParams.append('pageId', pageId);
	if (pageCode) queryParams.append('pageCode', pageCode);
	if (jobId) queryParams.append('jobId', jobId);
	if (startedDate) queryParams.append('startedDate', startedDate);
	if (endedDate) queryParams.append('endedDate', endedDate);
	if (tag) queryParams.append('tag', tag);
	if (skip) queryParams.append('skip', skip.toString());
	if (limit) queryParams.append('limit', limit.toString());

	const { data, status } = await CallAPI().get(
		'/applicants?' + queryParams.toString()
	);

	if (status !== 200) {
		throw new Error('Failed to fetch applicants');
	}

	return data;
};
