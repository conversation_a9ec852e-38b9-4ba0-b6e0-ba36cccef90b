import { CallAPI } from '@/configs/axios/axios';
import { TApplicantDetail } from '@/features/careers/schemas/applicant-schema';

export const getApplicantDetail = async (id: string): Promise<TApplicantDetail | null> => {
	try {
		const { data, status } = await CallAPI({apiKey: true}).get(`/client/applicants/detail/${id}`);

		if (status !== 200) {
			throw new Error('Failed to fetch applicant detail');
		}

		return data;
	} catch (error) {
		console.error('Error fetching applicant detail:', error);
		return null;
	}
};
