import z from 'zod';

export const acceptedFileTypes = ['application/pdf'];
export const acceptedLogoTypes = ['image/png', 'image/jpg', 'image/jpeg'];

// <PERSON><PERSON><PERSON> nghĩa kích thước tối đa
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB tính bằng byte
const MAX_LOGO_SIZE = 5 * 1024 * 1024; // 2MB tính bằng byte

export const emailFormSchema = z.object({
	domain: z.string().min(1, 'Domain is required'),
	prefix: z.string().min(1, 'Prefix is required'),
	file: z
		.instanceof(File)
		.refine((file) => acceptedFileTypes.includes(file.type), {
			message: 'File type not supported',
		})
		.refine((file) => file.size <= MAX_FILE_SIZE, {
			message: `File must be smaller than 5MB`,
		}),
	logo: z
		.instanceof(File)
		.refine((file) => acceptedLogoTypes.includes(file.type), {
			message: 'Logo type not supported',
		})
		.refine((file) => file.size <= MAX_LOGO_SIZE, {
			message: `Logo must be smaller than 5MB`,
		})
		.optional(),
	tawktoId: z.string().min(1, 'Tawkto ID is required'),
	expirationLink: z
		.string()
		.regex(/^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([\/\w .-]*)*\/?$/, {
			message: 'Invalid URL',
		})
		.optional(),
});

export type EmailForm = z.infer<typeof emailFormSchema>;
